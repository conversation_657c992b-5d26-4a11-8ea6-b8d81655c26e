#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

// File extensions to process
const extensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.scss'];

// Orange color mappings to theme variables
const orangeColorMappings = {
  // Tailwind orange classes
  'bg-orange-50': 'bg-primary-50',
  'bg-orange-100': 'bg-primary-100',
  'bg-orange-200': 'bg-primary-200',
  'bg-orange-300': 'bg-primary-300',
  'bg-orange-400': 'bg-primary-400',
  'bg-orange-500': 'bg-primary',
  'bg-orange-600': 'bg-primary-600',
  'bg-orange-700': 'bg-primary-700',
  'bg-orange-800': 'bg-primary-800',
  'bg-orange-900': 'bg-primary-900',
  
  // Text colors
  'text-orange-50': 'text-primary-50',
  'text-orange-100': 'text-primary-100',
  'text-orange-200': 'text-primary-200',
  'text-orange-300': 'text-primary-300',
  'text-orange-400': 'text-primary-400',
  'text-orange-500': 'text-primary',
  'text-orange-600': 'text-primary-600',
  'text-orange-700': 'text-primary-700',
  'text-orange-800': 'text-primary-800',
  'text-orange-900': 'text-primary-900',
  
  // Border colors
  'border-orange-50': 'border-primary-50',
  'border-orange-100': 'border-primary-100',
  'border-orange-200': 'border-primary-200',
  'border-orange-300': 'border-primary-300',
  'border-orange-400': 'border-primary-400',
  'border-orange-500': 'border-primary',
  'border-orange-600': 'border-primary-600',
  'border-orange-700': 'border-primary-700',
  'border-orange-800': 'border-primary-800',
  'border-orange-900': 'border-primary-900',
  
  // Hover states
  'hover:bg-orange-50': 'hover:bg-primary-50',
  'hover:bg-orange-100': 'hover:bg-primary-100',
  'hover:bg-orange-200': 'hover:bg-primary-200',
  'hover:bg-orange-300': 'hover:bg-primary-300',
  'hover:bg-orange-400': 'hover:bg-primary-400',
  'hover:bg-orange-500': 'hover:bg-primary',
  'hover:bg-orange-600': 'hover:bg-primary-600',
  'hover:bg-orange-700': 'hover:bg-primary-700',
  'hover:bg-orange-800': 'hover:bg-primary-800',
  'hover:bg-orange-900': 'hover:bg-primary-900',
  
  'hover:text-orange-50': 'hover:text-primary-50',
  'hover:text-orange-100': 'hover:text-primary-100',
  'hover:text-orange-200': 'hover:text-primary-200',
  'hover:text-orange-300': 'hover:text-primary-300',
  'hover:text-orange-400': 'hover:text-primary-400',
  'hover:text-orange-500': 'hover:text-primary',
  'hover:text-orange-600': 'hover:text-primary-600',
  'hover:text-orange-700': 'hover:text-primary-700',
  'hover:text-orange-800': 'hover:text-primary-800',
  'hover:text-orange-900': 'hover:text-primary-900',
  
  'hover:border-orange-50': 'hover:border-primary-50',
  'hover:border-orange-100': 'hover:border-primary-100',
  'hover:border-orange-200': 'hover:border-primary-200',
  'hover:border-orange-300': 'hover:border-primary-300',
  'hover:border-orange-400': 'hover:border-primary-400',
  'hover:border-orange-500': 'hover:border-primary',
  'hover:border-orange-600': 'hover:border-primary-600',
  'hover:border-orange-700': 'hover:border-primary-700',
  'hover:border-orange-800': 'hover:border-primary-800',
  'hover:border-orange-900': 'hover:border-primary-900',
};

// Hex color mappings to HSL theme variables
const hexOrangeColorMappings = {
  '#FFF7ED': 'hsl(var(--primary-50))',
  '#FFEDD5': 'hsl(var(--primary-100))',
  '#FED7AA': 'hsl(var(--primary-200))',
  '#FDBA74': 'hsl(var(--primary-300))',
  '#FB923C': 'hsl(var(--primary-400))',
  '#F97316': 'hsl(var(--primary))',
  '#EA580C': 'hsl(var(--primary-600))',
  '#C2410C': 'hsl(var(--primary-700))',
  '#9A3412': 'hsl(var(--primary-800))',
  '#7C2D12': 'hsl(var(--primary-900))',
  '#F26A1B': 'hsl(var(--primary))', // Brand color
  '#f26a1b': 'hsl(var(--primary))', // Brand color lowercase
};

// Additional complex patterns to replace
const complexOrangePatterns = [
  // Focus ring patterns
  {
    pattern: /focus:ring-orange-(\d+)/g,
    replacement: 'focus:ring-primary-$1'
  },
  // Ring offset patterns
  {
    pattern: /ring-orange-(\d+)/g,
    replacement: 'ring-primary-$1'
  },
  // Placeholder patterns
  {
    pattern: /placeholder-orange-(\d+)/g,
    replacement: 'placeholder-primary-$1'
  },
  // Divide patterns
  {
    pattern: /divide-orange-(\d+)/g,
    replacement: 'divide-primary-$1'
  },
  // From/to gradient patterns
  {
    pattern: /from-orange-(\d+)/g,
    replacement: 'from-primary-$1'
  },
  {
    pattern: /to-orange-(\d+)/g,
    replacement: 'to-primary-$1'
  },
  {
    pattern: /via-orange-(\d+)/g,
    replacement: 'via-primary-$1'
  },
  // Shadow patterns
  {
    pattern: /shadow-orange-(\d+)/g,
    replacement: 'shadow-primary-$1'
  },
  // Decoration patterns
  {
    pattern: /decoration-orange-(\d+)/g,
    replacement: 'decoration-primary-$1'
  },
  // Outline patterns
  {
    pattern: /outline-orange-(\d+)/g,
    replacement: 'outline-primary-$1'
  }
];

async function getFiles(dir) {
  const subdirs = await fs.readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      const stat = await fs.stat(res);
      return stat.isDirectory() ? getFiles(res) : res;
    })
  );
  return files
    .flat()
    .filter((file) => 
      extensions.includes(path.extname(file)) && 
      !file.includes('node_modules') && 
      !file.includes('.next') &&
      !file.includes('replace-hardcoded-orange-colors.js') // Exclude this script
    );
}

function replaceOrangeColors(content, filePath) {
  let modifiedContent = content;
  let replacementCount = 0;
  const replacements = [];

  // Replace Tailwind orange classes
  for (const [oldClass, newClass] of Object.entries(orangeColorMappings)) {
    const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, newClass);
      replacementCount += matches.length;
      replacements.push(`${oldClass} → ${newClass} (${matches.length} times)`);
    }
  }

  // Replace complex patterns
  for (const { pattern, replacement } of complexOrangePatterns) {
    const matches = modifiedContent.match(pattern);
    if (matches) {
      modifiedContent = modifiedContent.replace(pattern, replacement);
      replacementCount += matches.length;
      replacements.push(`Pattern ${pattern.source} → ${replacement} (${matches.length} times)`);
    }
  }

  // Replace hex colors
  for (const [hexColor, themeVar] of Object.entries(hexOrangeColorMappings)) {
    const regex = new RegExp(hexColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, themeVar);
      replacementCount += matches.length;
      replacements.push(`${hexColor} → ${themeVar} (${matches.length} times)`);
    }
  }

  return {
    content: modifiedContent,
    replacementCount,
    replacements
  };
}

async function main() {
  const isApply = process.argv.includes('--apply');
  const isBackup = process.argv.includes('--backup');
  
  console.log('🔍 Scanning for hardcoded orange colors...\n');
  
  const files = await getFiles('./src');
  let totalReplacements = 0;
  let modifiedFiles = 0;
  
  for (const file of files) {
    try {
      const content = await fs.readFile(file, 'utf8');
      const result = replaceOrangeColors(content, file);
      
      if (result.replacementCount > 0) {
        modifiedFiles++;
        totalReplacements += result.replacementCount;
        
        console.log(`📁 ${path.relative('.', file)}`);
        console.log(`   ${result.replacementCount} replacements:`);
        result.replacements.forEach(replacement => {
          console.log(`   • ${replacement}`);
        });
        console.log('');
        
        if (isApply) {
          if (isBackup) {
            await fs.writeFile(`${file}.backup`, content);
          }
          await fs.writeFile(file, result.content);
        }
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Files scanned: ${files.length}`);
  console.log(`   Files with orange colors: ${modifiedFiles}`);
  console.log(`   Total replacements: ${totalReplacements}`);
  
  if (!isApply) {
    console.log('\n💡 This was a dry run. Use --apply to make changes.');
    console.log('   Use --backup to create backup files before applying changes.');
  } else {
    console.log('\n✅ Changes applied successfully!');
    if (isBackup) {
      console.log('   Backup files created with .backup extension');
    }
  }
}

function showHelp() {
  console.log('Replace Hardcoded Orange Colors');
  console.log('');
  console.log('Usage:');
  console.log('  node replace-hardcoded-orange-colors.js                   # Dry run');
  console.log('  node replace-hardcoded-orange-colors.js --apply           # Apply changes');
  console.log('  node replace-hardcoded-orange-colors.js --apply --backup  # Apply with backup');
  console.log('');
  console.log('What it replaces:');
  console.log('  • Tailwind orange classes (bg-orange-500 → bg-primary)');
  console.log('  • Hex orange colors (#F97316 → hsl(var(--primary)))');
  console.log('  • CSS properties with orange colors');
  console.log('  • Complex Tailwind patterns (hover:, focus:, etc.)');
}

if (require.main === module) {
  if (process.argv.includes('--help')) {
    showHelp();
  } else {
    main();
  }
}

module.exports = { replaceOrangeColors, orangeColorMappings, hexOrangeColorMappings, complexOrangePatterns };
