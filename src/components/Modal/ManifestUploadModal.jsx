// components/Modal/ManifestUpdateModal.jsx
"use client";

import React, { useState, useEffect, useContext } from 'react';
import { 
  RefreshCw, 
  X, 
  AlertCircle, 
  Upload 
} from 'lucide-react';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import Cookies from 'js-cookie';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { getRepositoryField, updateRepositoryField, updateBranchManifest, getBranchManifest } from '@/utils/repositoryAPI';

const ManifestUpdateModal = ({ 
  projectId, 
  onClose, 
  onSuccess,
  selectedRepository = null,
  selectedBranch = null,
  title = "Update Project Manifest",
  description = "This manifest will be stored for the entire project and can include container definitions, build configurations, and project metadata."
}) => {
  const [manifestData, setManifestData] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [hasExistingManifest, setHasExistingManifest] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [error, setError] = useState('');
  const [controller, setController] = useState(null);
  const { showAlert } = useContext(AlertContext);

  // Check for existing manifest on component mount
  useEffect(() => {
    const checkExistingManifest = async () => {
      try {
        setIsLoading(true);

        if (selectedBranch ) {
          const branchManifest = await getBranchManifest(projectId, selectedRepository, selectedBranch);
          if (branchManifest.project_manifest) {
            setHasExistingManifest(true);
            setManifestData(branchManifest.project_manifest);
            setIsLoading(false);
            return;
          }
          else{
            setHasExistingManifest(false);
            setIsLoading(false);
            return;
          }
        }

        const response = await getRepositoryField(projectId, 'project_manifest');
        
        if (response.field_value) {
          setHasExistingManifest(true);
          // Handle both string and object manifest data
          const manifestContent = typeof response.field_value === 'string' 
            ? response.field_value 
            : JSON.stringify(response.field_value, null, 2);
          setManifestData(manifestContent);
        } else {
          setHasExistingManifest(false);
        }
      } catch (error) {
        // If field doesn't exist or error occurs, treat as no existing manifest
        setHasExistingManifest(false);
        
      } finally {
        setIsLoading(false);
      }
    };

    checkExistingManifest();
  }, [projectId]);

  // Cleanup controller on unmount
  useEffect(() => {
    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, [controller]);

  const handleGenerateManifest = async () => {
    setIsGenerating(true);
    setError('');
    setManifestData('');
    setGenerationProgress('Initializing manifest generation...');

    const abortController = new AbortController();
    setController(abortController);

    try {
      const url = `${process.env.NEXT_PUBLIC_API_URL}/repository/generate_manifest/${projectId}/`;
      
      await fetchEventSource(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${Cookies.get('idToken')}`,
          'Content-Type': 'application/json',
          'X-Tenant-Id': Cookies.get('tenant_id'),
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        signal: abortController.signal,
        openWhenHidden: true,
        onopen: (response) => {
          if (response.status !== 200) {
            setError('Failed to connect to manifest generation service');
            setIsGenerating(false);
            setGenerationProgress('');
            abortController.abort();
            return Promise.reject(new Error('Failed to connect'));
          }
          return Promise.resolve();
        },
        onmessage: (event) => {
          try {
            const data = JSON.parse(event.data);
            
            switch (data.status) {
              case 'starting':
              case 'progress':
                setGenerationProgress(data.message || 'Processing...');
                break;
                
              case 'streaming':
                if (data.content) {
                  setManifestData(prev => prev + data.content);
                }
                if (data.partial_manifest) {
                  setGenerationProgress('Streaming manifest content...');
                }
                break;
                
              case 'complete':
                setGenerationProgress('Generation complete!');
                if (data.manifest && data.manifest.content) {
                  setManifestData(data.manifest.content);
                }
                setTimeout(() => {
                  setIsGenerating(false);
                  setGenerationProgress('');
                  setHasExistingManifest(true);
                  abortController.abort();
                }, 1000);
                break;
                
              case 'yaml_ready':
                if (data.yaml_content) {
                  setManifestData(data.yaml_content);
                }
                break;
                
              case 'error':
                setError(data.message || 'Failed to generate manifest');
                setGenerationProgress('');
                setIsGenerating(false);
                abortController.abort();
                break;
                
              default:
                setGenerationProgress(data.message || 'Processing...');
            }
          } catch (parseError) {
            console.error('Error parsing manifest generation response:', parseError);
            setError('Error processing server response');
            setIsGenerating(false);
            setGenerationProgress('');
            abortController.abort();
          }
        },
        onerror: (error) => {
          console.error('Manifest generation error:', error);
          setError('Error during manifest generation');
          setIsGenerating(false);
          setGenerationProgress('');
          abortController.abort();
          return null;
        },
        onclose: () => {
          setController(null);
          if (isGenerating) {
            setIsGenerating(false);
            setGenerationProgress('');
          }
        }
      });
    } catch (error) {
      console.error('Error starting manifest generation:', error);
      setError(error.message || 'Failed to start manifest generation');
      setIsGenerating(false);
      setGenerationProgress('');
      abortController.abort();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!manifestData.trim()) {
      setError('Manifest data is required');
      return;
    }

    setIsUpdating(true);
    setError('');

    try {
      // Try to parse as YAML/JSON to validate format
      let parsedData;
      try {
        // If it looks like YAML (contains newlines and colons), keep as string
        // Otherwise try to parse as JSON
        if (manifestData.includes('\n') && manifestData.includes(':')) {
          parsedData = manifestData; // Keep as YAML string
        } else {
          parsedData = JSON.parse(manifestData); // Parse as JSON
        }
      } catch (parseError) {
        // If JSON parsing fails, treat as YAML string
        parsedData = manifestData;
      }
      if (selectedBranch) {
        await updateBranchManifest(projectId, selectedRepository, selectedBranch, parsedData);
      } else {
        await updateRepositoryField(projectId, 'project_manifest', parsedData);
      }

      
      showAlert('Project manifest updated successfully!', 'success');
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error updating manifest:', error);
      setError(error.message || 'Failed to update manifest');
      showAlert('Failed to update project manifest', 'error');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleStopGeneration = () => {
    if (controller) {
      controller.abort();
    }
    setIsGenerating(false);
    setGenerationProgress('');
    setError('');
  };

  const loadSampleManifest = () => {
    const sampleManifest = `overview:
  project_name: "sample-project"
  description: "A sample project manifest for demonstration purposes."
  third_party_services:
    - Analytics Service
    - Database Service
containers:
  - container_name: frontend
    description: "Frontend web application"
    interfaces: "HTTP (REST API), WebSocket for real-time updates"
    container_type: frontend
    dependent_containers:
      - backend
    workspace: frontend_workspace
    container_root: frontend_workspace/frontend
    port: "3000"
    framework: React
    type: ""
    buildCommand: "npm install && npm run build"
    startCommand: "npm start"
    installCommand: "npm install"
    lintCommand: "npm run lint"
    lintConfig: ""
    routes: []
    apiSpec: ""
    auth: null
    schema: ""
    migrations: ""
    seed: ""
    env: {}
  - container_name: backend
    description: "Backend API service"
    interfaces: "HTTP (REST API), Database connections"
    container_type: backend
    dependent_containers:
      - database
    workspace: backend_workspace
    container_root: backend_workspace/backend
    port: "8000"
    framework: FastAPI
    type: ""
    buildCommand: "pip install -r requirements.txt"
    startCommand: "uvicorn main:app --host 0.0.0.0 --port 8000"
    installCommand: "pip install -r requirements.txt"
    lintCommand: "flake8"
    lintConfig: ""
    routes: []
    apiSpec: ""
    auth: null
    schema: ""
    migrations: ""
    seed: ""
    env: {}`;
    
    setManifestData(sampleManifest);
  };

  const clearManifest = () => {
    setManifestData('');
    setHasExistingManifest(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col border border-semantic-gray-200">
        <div className="flex items-center justify-between border-b border-semantic-gray-200 px-6 py-4 bg-gradient-to-r from-primary-50 to-primary-50">
          <h2 className="typography-heading-4 font-weight-semibold text-semantic-gray-900">
            {hasExistingManifest ? title : 'Generate Project Manifest'}
          </h2>
          <button
            className="text-semantic-gray-400 hover:text-semantic-gray-600 rounded-md p-2 hover:bg-white/80 transition-colors"
            onClick={onClose}
            aria-label="Close"
            disabled={isGenerating}
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <div className="px-6 py-4 flex-1 min-h-0 flex flex-col">
            <div className="flex items-center justify-between mb-4">
              <label className="typography-body font-weight-medium text-semantic-gray-800">
                Project Manifest Data (YAML/JSON)
              </label>
              <div className="flex items-center gap-3">
                {!isGenerating && (
                  <>
                    {!hasExistingManifest && !selectedBranch && (
                      <button
                        type="button"
                        onClick={handleGenerateManifest}
                        className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium flex items-center gap-1"
                        disabled={isLoading}
                      >
                        <Upload className="w-3 h-3" />
                        Generate Manifest
                      </button>
                    )}

                    <button
                      type="button"
                      onClick={loadSampleManifest}
                      className="typography-body-sm text-primary-600 hover:text-primary-700 underline font-weight-medium"
                    >
                      Load Sample
                    </button>

                    {manifestData && (
                      <button
                        type="button"
                        onClick={clearManifest}
                        className="typography-body-sm text-red-600 hover:text-red-700 underline font-weight-medium"
                      >
                        Clear
                      </button>
                    )}
                  </>
                )}

                {isGenerating && (
                  <button
                    type="button"
                    onClick={handleStopGeneration}
                    className="px-4 py-2 typography-body-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center gap-2 shadow-sm"
                  >
                    <X className="w-4 h-4" />
                    Stop Generation
                  </button>
                )}
              </div>
            </div>

            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <RefreshCw className="w-6 h-6 animate-spin text-primary-600" />
                <span className="ml-3 typography-body text-semantic-gray-600">Loading existing manifest...</span>
              </div>
            )}

            {!isLoading && (
              <>
                {/* Generation Progress */}
                {isGenerating && generationProgress && (
                  <div className="mb-4 p-4 bg-gradient-to-r from-primary-50 to-primary-50 rounded-lg border border-primary-200">
                    <div className="flex items-center gap-3">
                      <RefreshCw className="w-5 h-5 animate-spin text-primary-600" />
                      <span className="typography-body text-primary-800 font-weight-medium">
                        {generationProgress}
                      </span>
                    </div>
                  </div>
                )}

                <textarea
                  value={manifestData}
                  onChange={(e) => {
                    if (!isGenerating) {
                      setManifestData(e.target.value);
                      setError('');
                    }
                  }}
                  placeholder={
                    isGenerating
                      ? "Generating manifest... Please wait..."
                      : hasExistingManifest
                        ? "Edit your existing project manifest..."
                        : "Enter project manifest data in YAML or JSON format, or generate one automatically..."
                  }
                  className={`flex-1 min-h-[400px] p-4 border-2 rounded-lg font-mono text-sm transition-colors resize-none ${
                    isGenerating
                      ? 'bg-semantic-gray-50 border-semantic-gray-200 cursor-not-allowed'
                      : 'bg-white border-semantic-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
                  }`}
                  readOnly={isGenerating}
                  required
                />
              </>
            )}
            
            {error && (
              <div className="mt-3 flex items-start p-3 bg-red-50 rounded-lg border border-red-200 text-red-800">
                <AlertCircle className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
                <p className="typography-body-sm">{error}</p>
              </div>
            )}


          </div>
          
          <div className="flex justify-end gap-3 px-6 py-4 border-t border-semantic-gray-200 bg-gradient-to-r from-semantic-gray-50 to-primary-50/30">
            <button
              type="button"
              className="px-6 py-2 typography-body-sm bg-white text-semantic-gray-700 border border-semantic-gray-300 rounded-lg hover:bg-semantic-gray-50 transition-colors shadow-sm"
              onClick={onClose}
              disabled={isUpdating || isGenerating}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`px-6 py-2 typography-body-sm text-white rounded-lg flex items-center gap-2 shadow-sm transition-colors ${
                isUpdating || isGenerating || isLoading
                  ? "bg-primary-300 cursor-not-allowed"
                  : "bg-primary-600 hover:bg-primary-700"
              }`}
              disabled={isUpdating || isGenerating || isLoading || !manifestData.trim()}
            >
              {isUpdating && <RefreshCw className="w-4 h-4 animate-spin" />}
              {isUpdating ? 'Updating...' : hasExistingManifest ? `Update ${selectedBranch ? 'Branch' : 'Project'}  Manifest` : 'Save Generated Manifest'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ManifestUpdateModal;