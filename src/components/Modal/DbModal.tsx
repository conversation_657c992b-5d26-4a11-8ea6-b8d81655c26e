// components/DatabaseAuthorization.tsx
'use client';

import React, { useState, useEffect } from 'react';
import { ChevronDown, Eye, EyeOff } from 'lucide-react';

interface DatabaseAuthFormData {
  databaseDriver: string;
  databaseUrl: string;
  username: string;
  password: string;
  connectionString: string;
}

interface DatabaseAuthorizationProps {
  initialData?: Partial<DatabaseAuthFormData>;
  onSubmit?: (data: DatabaseAuthFormData) => void;
  onCancel?: () => void;
}

const DatabaseAuthorization: React.FC<DatabaseAuthorizationProps> = ({
  initialData,
  onSubmit,
  onCancel
}) => {
  const [formData, setFormData] = useState<DatabaseAuthFormData>({
    databaseDriver: '',
    databaseUrl: '',
    username: '',
    password: '',
    connectionString: ''
  });

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Map initialData (from databaseInfo.properties) to form fields
  useEffect(() => {
    if (initialData) {
      const data = initialData as any;
      setFormData(prev => ({
        ...prev,
        databaseDriver: data.DatabaseType || prev.databaseDriver,
        databaseUrl: data.Host
          ? `${data.Host}${data.Port ? `:${data.Port}` : ''}`
          : prev.databaseUrl,
        username: data.Username || prev.username,
        password: data.PasswordRef || prev.password,
        connectionString: data.ConnectionString || prev.connectionString,
      }));
    }
  }, [initialData]);

  const databaseDrivers = [
    'MySQL',
    'PostgreSQL',
    'SQL Server',
    'Oracle',
    'MongoDB',
    'SQLite'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleDriverSelect = (driver: string) => {
    setFormData(prev => ({
      ...prev,
      databaseDriver: driver
    }));
    setDropdownOpen(false);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      // Reset to initial data or empty
      setFormData(initialData ? { ...initialData } as DatabaseAuthFormData : {
        databaseDriver: '',
        databaseUrl: '',
        username: '',
        password: '',
        connectionString: ''
      });
    }
  };

  const handleAuthorize = () => {
    if (onSubmit) {
      onSubmit(formData);
    } else {
      
    }
  };

  return (
      <div className="bg-white rounded-lg shadow-sm w-full max-w-xl sm:max-w-xl h-[80vh] px-4 sm:px-6 space-y-6 mx-2 sm:mx-0 flex flex-col">
        {/* Header */}
        <div className="sticky top-0 bg-white z-20 border-b border-semantic-gray-200 mt-4 p-4 -mx-4 -mt-4">
          <h1 className="text-lg sm:text-xl font-semibold text-semantic-gray-900">
            Database Authorization
          </h1>
          <p className="text-xs sm:text-sm text-semantic-gray-600 mt-1">
            Configure database connection parameters
          </p>
        </div>

        {/* Form */}
        <div className="space-y-4 sm:space-y-5 flex-1 overflow-y-auto min-h-0 scrollbar-hide">
          {/* Database Driver */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-semantic-gray-700 mb-1 sm:mb-2">
              Database Driver <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <button
                type="button"
                onClick={() => setDropdownOpen(!dropdownOpen)}
                className="w-full px-2 sm:px-3 py-2 text-left bg-white border border-semantic-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary flex items-center justify-between"
              >
                <span className={formData.databaseDriver ? 'text-semantic-gray-900' : 'text-semantic-gray-400'}>
                  {formData.databaseDriver || 'Select Database Driver'}
                </span>
                <ChevronDown className="h-5 w-5 text-semantic-gray-400" />
              </button>
              {dropdownOpen && (
                <div className="absolute z-10 mt-1 w-full bg-white border border-semantic-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                  {databaseDrivers.map((driver) => (
                    <button
                      key={driver}
                      type="button"
                      onClick={() => handleDriverSelect(driver)}
                      className="w-full px-3 py-2 text-left hover:bg-semantic-gray-50 focus:outline-none focus:bg-semantic-gray-50 text-xs sm:text-sm"
                    >
                      {driver}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Database URL */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-semantic-gray-700 mb-1 sm:mb-2">
              Database URL <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="databaseUrl"
              value={formData.databaseUrl}
              onChange={handleInputChange}
              placeholder="e.g., five-test-server.database.windows.net:1433"
              className="w-full px-2 sm:px-3 py-2 border border-semantic-gray-300 rounded-md shadow-sm placeholder-semantic-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary text-xs sm:text-sm"
            />
          </div>

          {/* Username */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-semantic-gray-700 mb-1 sm:mb-2">
              Username <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="Database username"
              className="w-full px-2 sm:px-3 py-2 border border-semantic-gray-300 rounded-md shadow-sm placeholder-semantic-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary text-xs sm:text-sm"
            />
          </div>

          {/* Password */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-semantic-gray-700 mb-1 sm:mb-2">
              Password <span className="text-red-500">*</span>
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder="Database password"
                className="w-full px-2 sm:px-3 py-2 border border-semantic-gray-300 rounded-md shadow-sm placeholder-semantic-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary text-xs sm:text-sm pr-10"
              />
              <button
                type="button"
                tabIndex={-1}
                className="absolute right-2 top-1/2 -translate-y-1/2 text-semantic-gray-400 hover:text-semantic-gray-600"
                onClick={() => setShowPassword((prev) => !prev)}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Connection String */}
          <div>
            <label className="block text-xs sm:text-sm font-medium text-semantic-gray-700 mb-1 sm:mb-2">
              Connection String (Optional)
            </label>
            <input
              type="text"
              name="connectionString"
              value={formData.connectionString}
              onChange={handleInputChange}
              placeholder="Full connection string - overrides individual fields above"
              className="w-full px-2 sm:px-3 py-2 border border-semantic-gray-300 rounded-md shadow-sm placeholder-semantic-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary text-xs sm:text-sm"
            />
          </div>
        </div>

        {/* Buttons */}
        <div className="flex gap-2 sm:gap-3 justify-end pt-2 flex-wrap sticky bottom-0 left-0 bg-white border-t border-semantic-gray-200 z-10 p-4 mt-auto">
          <button
            type="button"
            onClick={handleCancel}
            className="px-3 sm:px-4 py-2 text-semantic-gray-700 bg-white border border-semantic-gray-300 rounded-md hover:bg-semantic-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-semantic-gray-500 transition-colors text-xs sm:text-sm"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleAuthorize}
            className="px-3 sm:px-4 py-2 text-white bg-primary rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors text-xs sm:text-sm"
          >
            Authorize
          </button>
        </div>
      </div>
    
  );
};

export default DatabaseAuthorization;