"use client"

import { useState, useContext, useEffect, useCallback, useMemo } from 'react';
import { ArrowLeft, ArrowRight, Info, CheckCircle, AlertCircle, Upload, ChevronDown, ChevronUp } from 'lucide-react';
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from '../Context/StateContext';
import { getInterfaceData, createProjectGuidanceFlow, fetchNodeById,updateNodeByPriority } from '@/utils/api';
import { getInterfaceChildren } from "@/utils/architectureAPI";
import { ProjectSetupContext } from '../Context/ProjectSetupContext';
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import TableComponent from "@/components/SimpleTable/table";
import EmptyStateView from './EmptyStateModal';
import PropertiesRenderer from '../UiMetadata/PropertiesRenderer';
import en from "@/en.json"
import { ExecutionContext } from '../Context/ExecutionContext';
import StatusPanel from "@/components/StatusPanel/StatusPanel";
import InterfaceDefinition from '../BrowsePanel/Architecture/InterfaceDefinition';
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import APIDocumentation from "@/components/API/API"
import { cn } from '@/lib/utils';
import { buildProjectUrl } from '@/utils/navigationHelpers';

// Constants to avoid magic strings
const DISCUSSION_TYPES = {
  DESIGN_DETAILS: "design_details",
  DEFINITION: "definition"
};

const DISCUSSION_TYPE = [
  "design_details",
  "definition"
];

const CONFIG_STATE_MAPPING = {
  "design_details": "design_details_state",
  "definition": "definition_state"
};


const STORAGE_KEYS = {
  DESIGN_DETAILS: (projectId, interfaceId) => `interface_design_details_${projectId}_${interfaceId}`,
  DEFINITION: (projectId, interfaceId) => `interface_definition_${projectId}_${interfaceId}`,
  CONFIGURED: (projectId, interfaceId) => `interface_configured_${projectId}_${interfaceId}`,
  SKIPPED_INTERFACES: (projectId) => `skippedInterfaces_${projectId}`
};

export default function InterfaceConfigurationStep({ type }) {
  // State management
  const [interfaces, setInterfaces] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [interfaceDiscussionType, setInterfaceDiscussionType] = useState(DISCUSSION_TYPES.DESIGN_DETAILS);
  const [configMethod, setConfigMethod] = useState('discussion');
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const [skippedInterfaces, setSkippedInterfaces] = useState([]);
  const [showSkipNotification, setShowSkipNotification] = useState(false);
  const [showAllSkippedNotification, setShowAllSkippedNotification] = useState(false);
  const [interfaceCount, setInterfaceCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedInterfaceData, setSelectedInterfaceData] = useState(null);
  const [showInterfaceDetails, setShowInterfaceDetails] = useState(false)
  const [metaData, setMetaData] = useState(null)
  const [interfaceChildren, setInterfaceChildren] = useState([]);

  // Context hooks
  const { projectId, showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
  const { showAlert } = useContext(AlertContext);
  const { setIsVertCollapse } = useContext(StateContext);
  const [manualNavigation, setManualNavigation] = useState(false);
  const [isOpen, setIsOpen] = useState(true);
  const toggleAccordion = () => { setIsOpen(!isOpen); };

  // Navigation hooks
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  const isCreatingInterface = searchParams.has("is_creating_Interface");
  const nodeId = searchParams.get("node_id");
  const selectedInterfaceId = searchParams.get("selectedInterfaceId");
  const [currentDiscussionIndex, setCurrentDiscussionIndex] = useState(0);
  const [completedDiscussions, setCompletedDiscussions] = useState({});
  const [allDiscussionsComplete, setAllDiscussionsComplete] = useState(false);
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");


  const currentDiscussionType = DISCUSSION_TYPE[currentDiscussionIndex];

  const getDiscussionTitle = (type) => {
    switch (type) {
      case "design_details": return "Interface Design";
      case "definition": return "Interface Definition";
      default: return "Discussion";
    }
  };

  // Memoized current interface data to avoid recalculation
  const currentInterfaceData = useMemo(() => {
    return interfaces.length > 0 && currentIndex < interfaces.length
      ? interfaces[currentIndex]
      : null;
  }, [interfaces, currentIndex]);

  // Fetch interface data from API
  const fetchInterfaceData = useCallback(async () => {
    if (!projectId) return;

    setIsLoading(true);
    try {
      const response = await getInterfaceData(projectId);
      if (searchParams.has("selectedInterfaceId")) {
        const interfaceId = searchParams.get("selectedInterfaceId");
        const detailedData = await fetchNodeById(interfaceId, "Interface")
        setMetaData(detailedData.ui_metadata)

      }

      // Extract interfaces from the nested structure
      let interfaces = [];

      // Handle the nested structure - response is an array with objects that have an 'interfaces' property
      if (Array.isArray(response) && response.length > 0 && response[0].interfaces) {
        const rawInterfaces = response[0].interfaces || [];
        const uniqueInterfacesMap = new Map();

        rawInterfaces.forEach((item) => {
          if (item?.child_node?.id && !uniqueInterfacesMap.has(item?.child_node?.id)) {
            uniqueInterfacesMap.set(item?.child_node?.id, item);
          }
        });
        interfaces = Array.from(uniqueInterfacesMap.values());

      } else if (Array.isArray(response)) {
        // In case the API returns a flat array directly
        interfaces = response;

      } else {


        setIsLoading(false);
        return;
      }

      // Validate that each interface has an ID
      if (interfaces.some(item => !item.id)) {

        // Add fallback IDs if missing to prevent UI issues
        interfaces.forEach((item, index) => {
          if (!item.id) {
            item.id = `temp-${index + 1}`;
          }
        });
      }

      // Get skipped interfaces from session storage
      const storedSkips = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SKIPPED_INTERFACES(projectId)) || '[]');

      // Filter out skipped interfaces
      const filteredInterfaces = interfaces.filter(i => !storedSkips.includes(i.id));



      // Set state with the extracted and filtered interfaces
      setInterfaces(filteredInterfaces);
      setInterfaceCount(interfaces.length);
      setSkippedInterfaces(storedSkips);

      // If we have interfaces, set the current one
      if (filteredInterfaces.length > 0 && (currentIndex >= filteredInterfaces.length || currentIndex < 0)) {
        // If current index is out of bounds after filtering, reset it
        setCurrentIndex(0);
      }

    } catch (error) {
      console.error("error in loading interfaces", error)
    } finally {
      setIsLoading(false);
    }
  }, [projectId, showAlert, currentIndex, manualNavigation]);

  const fetchInterfaceChildData = async () => {
    try {
      if (currentInterfaceData) {
        const children = await getInterfaceChildren(currentInterfaceData?.child_node?.id);
        setInterfaceChildren(children);
      }
    } catch (error) {
      console.error("error in fetching interface childern", error)
    }
  }

  // Initial data fetch
  useEffect(() => {
    if (projectId) {

      fetchInterfaceData();
      fetchInterfaceChildData()
    } else {

      showAlert('Project ID is missing. Please refresh the page or contact support.', 'error');
    }
  }, [fetchInterfaceData, projectId, showAlert, searchParams]);

  // Handle URL parameter changes
  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    // const isCreatingInterface = searchParams.has("is_creating_Interface");
    // const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");
    const discussionType = searchParams.get("discussionType");

    if (nodeId && nodeType === "Interface" && !hasDiscussionParam && !isCreatingInterface && discussionType) {
      handleDiscussionCompletion(nodeId, discussionType);
    }
  }, [searchParams, projectId, interfaces]);

  // Log completion and update storage
  const logCompletion = useCallback(async (interfaceId, discussionType) => {
    const storageKey = discussionType === DISCUSSION_TYPES.DESIGN_DETAILS
      ? STORAGE_KEYS.DESIGN_DETAILS(projectId, interfaceId)
      : STORAGE_KEYS.DEFINITION(projectId, interfaceId);

    sessionStorage.setItem(storageKey, "completed");

    const stepName = discussionType === DISCUSSION_TYPES.DESIGN_DETAILS
      ? "interface_design_details"
      : "interface_definition";

    try {
      await createProjectGuidanceFlow(parseInt(projectId), {
        project_id: parseInt(projectId),
        step_name: stepName,
        status: "completed",
        data: {
          interface_id: parseInt(interfaceId),
          type: "Interface",
          status: "configured",
          discussion_type: discussionType
        }
      });

      // Check if both parts are now complete
      checkAndMoveToNextInterface(interfaceId, discussionType);

      // Refresh data
      fetchInterfaceData();
    } catch (error) {

    }
  }, [projectId, fetchInterfaceData, searchParams]);

  // Handle discussion completion
  const handleDiscussionCompletion = useCallback((nodeId, discussionType) => {
    logCompletion(nodeId, discussionType);
  }, [logCompletion]);

  // Check if both discussions are complete and move to next interface if needed
  const checkAndMoveToNextInterface = useCallback((interfaceId, currentDiscussionType) => {
    const designDetailsComplete = sessionStorage.getItem(STORAGE_KEYS.DESIGN_DETAILS(projectId, interfaceId)) === "completed";
    const definitionComplete = sessionStorage.getItem(STORAGE_KEYS.DEFINITION(projectId, interfaceId)) === "completed";

    if (designDetailsComplete && definitionComplete) {
      // Mark the whole interface as configured
      sessionStorage.setItem(STORAGE_KEYS.CONFIGURED(projectId, interfaceId), "true");

      // Reset to design_details for next interface
      setInterfaceDiscussionType(DISCUSSION_TYPES.DESIGN_DETAILS);

      // Move to next interface if not the last one
      const interfaceIndex = interfaces.findIndex(i => i.id.toString() === interfaceId);
      if (interfaceIndex >= 0 && interfaceIndex < interfaces.length - 1) {
        setCurrentIndex(interfaceIndex + 1);
      }
    } else {
      // Toggle to other discussion type
      setInterfaceDiscussionType(
        currentDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS
          ? DISCUSSION_TYPES.DEFINITION
          : DISCUSSION_TYPES.DESIGN_DETAILS
      );
    }
  }, [projectId, interfaces]);
  const handlePropertyUpdate = async (key, value) => {
  const interfaceId = currentInterfaceData?.child_node?.id;
  if (!interfaceId) return;

  try {
    const response = await updateNodeByPriority(interfaceId, key, value);

    if (response === "success" || response?.status === "success") {
      showAlert("Content updated successfully", "success");

      // Refresh interface data
      const updated = await fetchNodeById(interfaceId, "Interface");
      if (updated) {
        setSelectedInterfaceData(updated);
        setMetaData(updated.ui_metadata);
      }
    } else {
      showAlert("Failed to update content", "error");
    }
  } catch (error) {
    showAlert("Failed to update content", "error");
  }
};

  useEffect(() => {
    if (selectedInterfaceId && interfaces.length > 0 && !manualNavigation) {
      const index = interfaces.findIndex(interfaceItem =>
        interfaceItem.child_node &&
        interfaceItem.child_node.id.toString() === selectedInterfaceId);
      if (index !== -1) {
        setCurrentIndex(index);
        setShowInterfaceDetails(true)
        // Reset to first discussion type when navigating
        setInterfaceDiscussionType(DISCUSSION_TYPES.DESIGN_DETAILS);
      }
    }
  }, [selectedInterfaceId, interfaces, manualNavigation]);

  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])

  // UI event handlers
  const handleCloseModal = () => setConfigureModel(false);

  const handleConfigureClick = () => {
    setConfigMethod('auto');
    setConfigureModel(true);
  };

  const cleanDescription = (description) => {
    if (!description) return '';

    return description
      .replace(/#+\s/g, '')        // Remove markdown headers (# Header)
      .replace(/\*\*/g, '')        // Remove bold (**text**)
      .replace(/\*/g, '')          // Remove italics (*text*)
      .replace(/`/g, '')           // Remove code ticks (`code`)
      .replace(/\n\n/g, ' ')       // Replace double line breaks with space
      .replace(/\n-\s/g, ', ')     // Replace bullet points with commas
      .replace(/\n\d+\.\s/g, ', ') // Replace numbered lists with commas
      .replace(/\n/g, ' ')         // Replace remaining line breaks with spaces
      .replace(/\s{2,}/g, ' ')     // Replace multiple spaces with single space
      .trim();                     // Trim extra whitespace
  };

  useEffect(() => {
    const hasDiscussionParam = searchParams.has("discussion");
    // const isCreatingInterface = searchParams.has("is_creating_Interface");
    // const nodeId = searchParams.get("node_id");
    const nodeType = searchParams.get("node_type");
    const discussionType = searchParams.get("discussionType");

    if (nodeId && nodeType === "Interface" && !hasDiscussionParam && !isCreatingInterface && discussionType) {
      handleDiscussionCompletion(nodeId, discussionType);
    }

    // If we have a nodeId but are not creating an interface, fetch details
    if (nodeId && nodeType === "Interface" && !isCreatingInterface) {
      const fetchDetails = async () => {
        const detailedData = await fetchNodeById(nodeId, "Interface");
        if (detailedData) {
          setSelectedInterfaceData(detailedData);
          if (discussionType === "design_details") {
            setCurrentDiscussionIndex(1); // Move to definition discussion
          } else {
            setAllDiscussionsComplete(true);
          }
        }
      };
      fetchDetails();
    }
  }, [searchParams, projectId, interfaces]);

  const handleUpdateInterface = useCallback((id) => {
    if (!id) return;

    setConfigMethod('discussion');

    // Create search params
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", id);
    newSearchParams.set("node_type", "Interface");
    newSearchParams.set("discussionType", currentDiscussionType);
    newSearchParams.set("is_creating_Interface", "true");


    // Check current pathname format
    if (pathname.includes('/architecture/interfaces/')) {
      // If already on the correct URL format, just append search params
      router.push(`${pathname}?${newSearchParams.toString()}`);
    } else {
      // Navigate to the correct URL format with the interface ID
      router.push(`${buildProjectUrl(projectId, `architecture/interfaces/${id}`)}?${newSearchParams.toString()}`);
    }
  }, [searchParams, interfaceDiscussionType, router, pathname]);

  // Check if interface is complete
  const isInterfaceComplete = useCallback((interfaceId) => {
    const designDetailsComplete = sessionStorage.getItem(STORAGE_KEYS.DESIGN_DETAILS(projectId, interfaceId)) === "completed";
    const definitionComplete = sessionStorage.getItem(STORAGE_KEYS.DEFINITION(projectId, interfaceId)) === "completed";
    return designDetailsComplete && definitionComplete;
  }, [projectId]);

  // Check if current discussion is complete
  const isCurrentInterfaceDiscussionComplete = useCallback(() => {
    if (!currentInterfaceData) return false;

    const interfaceId = currentInterfaceData.id;
    return sessionStorage.getItem(
      interfaceDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS
        ? STORAGE_KEYS.DESIGN_DETAILS(projectId, interfaceId)
        : STORAGE_KEYS.DEFINITION(projectId, interfaceId)
    ) === "completed";
  }, [currentInterfaceData, interfaceDiscussionType, projectId, searchParams]);

  // Handle skipping current interface
  const handleSkip = useCallback(() => {
    if (!currentInterfaceData) return;

    const currentId = currentInterfaceData.id;

    // If this interface is already completed, just move to the next
    if (isInterfaceComplete(currentId)) {
      handleNext();
      return;
    }

    // Mark interface as skipped
    const updatedSkipped = [...skippedInterfaces, currentId];
    sessionStorage.setItem(STORAGE_KEYS.SKIPPED_INTERFACES(projectId), JSON.stringify(updatedSkipped));
    setSkippedInterfaces(updatedSkipped);
    setShowSkipNotification(true);

    // Log skipped interface
    createProjectGuidanceFlow(parseInt(projectId), {
      project_id: parseInt(projectId),
      step_name: "interface_skipped",
      status: "completed",
      data: {
        interface_id: parseInt(currentId),
        type: "Interface",
        status: "skipped"
      }
    }).catch(error => {

    });

    // After 3 seconds, remove this interface and reset
    setTimeout(() => {
      setShowSkipNotification(false);
      const newInterfaces = interfaces.filter(i => i.id !== currentId);
      setInterfaces(newInterfaces);

      // Adjust current index if needed
      if (currentIndex >= newInterfaces.length) {
        setCurrentIndex(Math.max(0, newInterfaces.length - 1));
      }
    }, 3000);
  }, [currentInterfaceData, isInterfaceComplete, skippedInterfaces, projectId, interfaces, currentIndex]);

  // Handle skipping all interfaces
  const handleSkipAll = useCallback(() => {
    if (interfaces.length === 0) return;

    const allIds = interfaces.map(i => i.id);
    const updatedSkipped = [...skippedInterfaces, ...allIds];

    // Mark all as skipped in storage
    sessionStorage.setItem(STORAGE_KEYS.SKIPPED_INTERFACES(projectId), JSON.stringify(updatedSkipped));
    setSkippedInterfaces(updatedSkipped);
    setShowAllSkippedNotification(true);

    // Log all skipped interfaces
    createProjectGuidanceFlow(parseInt(projectId), {
      project_id: parseInt(projectId),
      step_name: "interfaces_skipped",
      status: "completed",
      data: {
        skipped_interfaces: allIds.map(id => parseInt(id)),
        type: "Interface",
        status: "skipped"
      }
    }).catch(error => {

    });

    // Clear interfaces after a brief delay
    setTimeout(() => {
      setInterfaces([]);
      setCurrentIndex(0);
    }, 50);
  }, [interfaces, skippedInterfaces, projectId]);

  // Navigation handlers
  const handleNext = () => {
    setManualNavigation(true)
    const nextIndex = Math.min(currentIndex + 1, interfaces.length - 1);
    setCurrentIndex(nextIndex)
    if (interfaces.length > 0 && nextIndex < interfaces.length) {
      const nextInterfaceId = interfaces[nextIndex].child_node?.id;
      if (nextInterfaceId) {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("selectedInterfaceId", nextInterfaceId);
        router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });
      }
    }


  };

  const handlePrevious = () => {
    setManualNavigation(true)
    const prevIndex = Math.max(0, currentIndex - 1);
    setCurrentIndex(prevIndex);
    if (interfaces.length > 0 && prevIndex >= 0) {
      const prevInterfaceId = interfaces[prevIndex].child_node?.id;
      if (prevInterfaceId) {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("selectedInterfaceId", prevInterfaceId);
        router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });
      }
    }


  };

  const isDiscussionCompleted = useCallback((interfaceId, discussionType) => {
    if (!currentInterfaceData) return false;

    const configKey = CONFIG_STATE_MAPPING[discussionType];
    return (
      currentInterfaceData.child_node?.properties?.[configKey] === "configured" ||
      sessionStorage.getItem(
        discussionType === "design_details"
          ? STORAGE_KEYS.DESIGN_DETAILS(projectId, interfaceId)
          : STORAGE_KEYS.DEFINITION(projectId, interfaceId)
      ) === "completed"
    );
  }, [currentInterfaceData, projectId]);

  // Effect to automatically update the current discussion index based on completion
  useEffect(() => {
    if (!currentInterfaceData || !currentInterfaceData.child_node) return;

    const interfaceId = currentInterfaceData.child_node.id;

    // Check if the first discussion is completed
    const isFirstDiscussionCompleted = isDiscussionCompleted(interfaceId, "design_details");

    // Check if the second discussion is completed
    const isSecondDiscussionCompleted = isDiscussionCompleted(interfaceId, "definition");

    if (isFirstDiscussionCompleted && !isSecondDiscussionCompleted) {
      // First discussion completed but second is not - show second discussion
      setCurrentDiscussionIndex(1);
    } else if (!isFirstDiscussionCompleted) {
      // First discussion not completed - show first discussion
      setCurrentDiscussionIndex(0);
    } else if (isFirstDiscussionCompleted && isSecondDiscussionCompleted) {
      // Both discussions completed - show completed state
      setAllDiscussionsComplete(true);
    }
  }, [currentInterfaceData, isDiscussionCompleted, searchParams]);



  const handleBackToInterfaceList = () => {

    setShowInterfaceDetails(false);
    setManualNavigation(false)
    const newSearchParams = new URLSearchParams(searchParams);

    if (newSearchParams.has("selectedInterfaceId")) {
      newSearchParams.delete("selectedInterfaceId");
    }
    router.replace(`${pathname}?${newSearchParams.toString()}`, { scroll: false });

  };

  // Success callback for configuration
  const handleConfigureSuccess = useCallback(() => {
    if (!currentInterfaceData) return;

    const successMessage = `Interface ${interfaceDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS ? "Design Details" : "Definition"} Configured Successfully`;
    showAlert(successMessage, "success");

    // Log completion
    logCompletion(currentInterfaceData.id, interfaceDiscussionType);
  }, [currentInterfaceData, interfaceDiscussionType, showAlert, logCompletion]);

  // Navigation status
  const isFirstInterface = currentIndex === 0;
  const isLastInterface = currentIndex === interfaces.length - 1 || interfaces.length === 0;

  // Table configuration
  const tableHeaders = [
    { key: 'id', label: 'Id' },
    { key: 'title', label: 'Title' },
    // { key: 'type', label: 'Type' },
    { key: 'description', label: 'Description' },
  ];

  // Memoized table data
  const tableData = useMemo(() => {
    return interfaces.map(i => ({
      id: i.child_node?.id,
      title: i.child_node?.properties?.Title ||
        i.properties?.Title ||
        'Untitled Interface',
      // type: i.child_node?.type ||
      //   i.type ||
      //   'Interface',
      description: (cleanDescription(i.child_node?.properties?.Description) ||
        cleanDescription(i.properties?.Description) ||
        'No description').substring(0, 100) + '...'
    }));
  }, [interfaces]);

  // Component renderers
  const renderInterfaceSkippedNotification = () => (
    <div className="max-w-3xl mx-auto p-4">
      <div className="border border-cyan-200 bg-cyan-50 rounded-lg p-4 flex items-start gap-3">
        <div className="text-semantic-gray-500">
          <Info size={24} />
        </div>
        <div>
          <p className="text-semantic-gray-800 font-weight-medium">
            Interface skipped: You can configure "{currentInterfaceData?.child_node?.properties?.Title || 'this interface'}" later in the workflow manager.
          </p>
        </div>
      </div>
    </div>
  );

  const renderAllInterfacesSkippedNotification = () => (
    <div className="border-l-4 border-green-500 bg-green-50 p-4 mb-8 flex items-start gap-3">
      <div className="text-semantic-gray-500">
        <Info size={24} />
      </div>
      <div>
        <p className="text-semantic-gray-800">
          All interfaces skipped: You can configure all interfaces later in the workflow manager.
        </p>
      </div>
    </div>
  );

  const renderSuccessBadge = (discussionType) => (
    <div className="max-w-3xl mx-auto p-4">
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border-l-4 border-emerald-500 rounded-lg p-5 shadow-md">
        <div className="flex items-start gap-4">
          <div className="bg-green-500 text-white p-2 rounded-full flex-shrink-0">
            <CheckCircle size={24} />
          </div>
          <div>
            <h3 className="font-weight-bold typography-body-lg text-semantic-gray-800 flex items-center gap-2">
              {discussionType === DISCUSSION_TYPES.DESIGN_DETAILS ? "Interface Design Details" : "Interface Definition"} completed!
              <span className="inline-flex px-2 py-1 bg-green-100 text-green-800 typography-caption font-weight-medium rounded-full">
                Complete
              </span>
            </h3>
            <p className="text-semantic-gray-600 mt-2">
              {discussionType === DISCUSSION_TYPES.DESIGN_DETAILS
                ? "The interface design details have been configured successfully."
                : "The interface definition has been completed successfully."
              }
            </p>
            <p className="text-semantic-gray-600 mt-1">
              {discussionType === DISCUSSION_TYPES.DESIGN_DETAILS && !isInterfaceComplete(currentInterfaceData?.id)
                ? "Next, you need to complete the interface definition."
                : discussionType === DISCUSSION_TYPES.DEFINITION && !isInterfaceComplete(currentInterfaceData?.id)
                  ? "Next, you need to complete the interface design details."
                  : "This interface is fully configured. You can proceed to the next interface."
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const LoadingSpinner = () => (
    <div className="w-full animate-pulse rounded-lg overflow-hidden border border-semantic-gray-200">
      {/* Table header */}
      <div className="flex items-center p-4 border-b bg-semantic-gray-50 rounded-t-lg">
        <div className="w-5 h-5 bg-semantic-gray-200 rounded mr-3"></div>
        <div className="flex-1 grid grid-cols-5 gap-4">
          <div className="h-6 bg-semantic-gray-200 rounded col-span-2"></div>
          <div className="h-6 bg-semantic-gray-200 rounded"></div>
          <div className="h-6 bg-semantic-gray-200 rounded"></div>
          <div className="h-6 bg-semantic-gray-200 rounded"></div>
        </div>
      </div>

      {/* Table rows */}
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center p-4 border-b hover:bg-semantic-gray-50">
          <div className="w-5 h-5 bg-semantic-gray-200 rounded mr-3"></div>
          <div className="flex-1 grid grid-cols-5 gap-4">
            <div className="col-span-2">
              <div className="h-5 bg-semantic-gray-200 rounded mb-2 w-3/4"></div>
              <div className="h-4 bg-semantic-gray-200 rounded w-1/2"></div>
            </div>
            <div className="flex items-center">
              <div className="h-6 w-16 bg-semantic-gray-200 rounded"></div>
            </div>
            <div className="h-5 bg-semantic-gray-200 rounded w-1/2"></div>
            <div className="h-5 bg-semantic-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      ))}

      {/* Table footer / pagination */}
      <div className="flex justify-between items-center p-4 bg-semantic-gray-50 rounded-b-lg">
        <div className="h-8 w-32 bg-semantic-gray-200 rounded"></div>
        <div className="flex space-x-2">
          <div className="h-8 w-8 bg-semantic-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-semantic-gray-200 rounded"></div>
          <div className="h-8 w-8 bg-semantic-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );

  function LoadingSkeleton() {

    return (

      <div className="bg-white rounded-lg shadow-md p-6 mb-8 animate-pulse">

        <div className="flex justify-between items-center mb-4">

          <div className="h-6 w-24 bg-semantic-gray-200 rounded"></div>

          <div className="h-8 w-20 bg-semantic-gray-200 rounded"></div>

        </div>

        <div className="h-8 w-3/4 bg-semantic-gray-200 rounded mb-4"></div>

        <div className="flex space-x-2 mb-6">

          <div className="h-6 w-16 bg-semantic-gray-200 rounded-full"></div>

          <div className="h-6 w-20 bg-semantic-gray-200 rounded-full"></div>

        </div>

        <div className="mb-6">

          <div className="h-6 w-1/2 bg-semantic-gray-200 rounded mb-4"></div>

          <div className="h-4 w-full bg-semantic-gray-200 rounded mb-2"></div>

          <div className="h-4 w-5/6 bg-semantic-gray-200 rounded"></div>

        </div>

        <div className="flex flex-col md:flex-row gap-6 justify-center md:justify-start">

          <div className="w-[300px] h-[200px] bg-semantic-gray-100 rounded-lg p-6">

            <div className="flex items-center space-x-2 mb-4">

              <div className="w-8 h-8 bg-semantic-gray-200 rounded-lg"></div>

              <div className="h-6 w-32 bg-semantic-gray-200 rounded"></div>

            </div>

            <div className="h-4 w-full bg-semantic-gray-200 rounded mb-2"></div>

            <div className="h-4 w-5/6 bg-semantic-gray-200 rounded mb-2"></div>

            <div className="h-4 w-4/6 bg-semantic-gray-200 rounded"></div>

          </div>

          <div className="w-[300px] h-[200px] bg-semantic-gray-100 rounded-lg p-6">

            <div className="flex items-center space-x-2 mb-4">

              <div className="w-8 h-8 bg-semantic-gray-200 rounded-lg"></div>

              <div className="h-6 w-32 bg-semantic-gray-200 rounded"></div>

            </div>

            <div className="h-4 w-full bg-semantic-gray-200 rounded mb-2"></div>

            <div className="h-4 w-5/6 bg-semantic-gray-200 rounded mb-2"></div>

            <div className="h-4 w-4/6 bg-semantic-gray-200 rounded"></div>

          </div>

        </div>

      </div>

    );

  }

  const HeaderDefineSection = () => (

    <div className="border border-semantic-gray-200 rounded-lg mb-4">
      <button
        onClick={toggleAccordion}
        className={cn(
          'w-full flex items-center justify-between px-4 py-1 text-left rounded-t-lg transition-colors duration-200',
          'hover:bg-semantic-gray-100'
        )}
        type="button"
        aria-expanded={isOpen}
      >
        <span className="flex items-center gap-2 font-weight-semibold typography-body text-[hsl(var(--semantic-gray-700))]">
          Interface Definition
        </span>
        <div className="flex items-center gap-4">
          <span className="text-semantic-gray-500">
            {isOpen ? (
              <ChevronUp className="h-5 w-5" />
            ) : (
              <ChevronDown className="h-5 w-5" />
            )}
          </span>
        </div>
      </button>
      {isOpen && (
        <div className="p-4 bg-white border-t border-semantic-gray-200 rounded-b-lg typography-body-sm text-[#464F60] font-weight-medium">
          {interfaceChildren ? (
            <>
              <InterfaceDefinition data={interfaceChildren} />
            </>
          ) : (
            <div className="interface-definition-tab-content-no-content-found">
              <span>
                <EmptyStateView type="interfaceDefinition" />
              </span>
            </div>
          )}
        </div>
      )}
    </div>

  );

  const renderConfiguredInterface = (interfaceData) => (
    <div>
      <PropertiesRenderer
        properties={interfaceData?.child_node?.properties}
        metadata={metaData}
        to_skip={["Title", "PublicAPIDetails", "Type"]}
        onUpdate={handlePropertyUpdate}
      />

      <Accordion title="Public API Reference" defaultOpen={true}>
        <APIDocumentation apiDetails={interfaceData?.child_node?.properties?.PublicAPIDetails} />
      </Accordion>
      <HeaderDefineSection />

      {/* Public Interfaces Section */}
      {/* <div id="public-interfaces" className="space-y-4 mt-6">
        {interfaceData.properties ? (
          <>
            <TableComponent
              data={parseInterfaces(interfaceData)}
              headers={interfaceTableHeaders}
              sortableColumns={{
                id: true,
                title: true,
                definition_state: true,
              }}
              itemsPerPage={20}
              onRowClick={(rowId) => {}}
              title="Public Interfaces"
            />

            <APIDocumentation
              apiDetails={interfaceData.properties.PublicAPIDetails}
            />
          </>
        ) : (
          <div className="mt-5 mb-3">
            <div className="border rounded-lg p-6 flex flex-col items-center justify-center bg-semantic-gray-50">
              <p className="text-semantic-gray-500">No interfaces are currently available</p>
            </div>
          </div>
        )}
      </div> */}

      {/* Connected Components Section */}

    </div>
  );

  const renderInterfaceTable = () => (
    <div className="my-4">
      <TableComponent
        data={tableData}
        onRowClick={async id => {
          if (type === "InterfaceList") {
            // Navigate to Interface Overview with the selected interface
            const detailedData = await fetchNodeById(id, "Interface")
            const children = await getInterfaceChildren(id);
            setInterfaceChildren(children);
            setMetaData(detailedData.ui_metadata)
            const newSearchParams = new URLSearchParams(searchParams);
            newSearchParams.set("stepName", "Interface Details");
            newSearchParams.set("selectedInterfaceId", id);
            router.push(`${pathname}?${newSearchParams.toString()}`);
          }
        }}
        headers={tableHeaders}
        sortableColumns={{ "id": true, "title": true, "type": true }}
        itemsPerPage={10}
        title="Project Interfaces"
      />
    </div>
  );

  // Loading state
  if (isLoading && !showInterfaceDetails) {
    return <LoadingSpinner />;
  }

  if (isLoading && showInterfaceDetails) {
    return <LoadingSkeleton />;
  }

  if (!showInterfaceDetails) {
    return (
      <div className="p-6 h-full  overflow-y-auto bg-white">
        <div className="mx-auto w-full">
          <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mb-4">
            Interfaces List
          </h2>

          {interfaces.length > 0 ? (
            renderInterfaceTable()
          ) : (
            <div>
              <EmptyStateView type="architectureInterfaces" />
            </div>
          )}
        </div>
      </div>
    );
  }

  const isInterfaceConfigured = (interfaceData) => {
    return interfaceData &&
      interfaceData.child_node.properties &&
      interfaceData.child_node.properties.design_details_state === "configured" &&
      interfaceData.child_node.properties.definition_state === "configured";
  };

  //   if (nodeId && !isCreatingInterface && selectedInterfaceData && 
  //     isInterfaceConfigured(selectedInterfaceData)) {
  //   return (
  //     <div className="p-6 h-full  overflow-y-auto px-4 bg-white">
  //       <div className="mx-auto w-full">
  //         <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mb-4">
  //           {selectedInterfaceData.properties?.Title || "Interface Details"}
  //         </h2>

  //         {renderConfiguredInterface(selectedInterfaceData)}
  //       </div>
  //     </div>
  //   );
  // }



  return (
    <div className="p-6 h-full  overflow-y-auto px-4 bg-white">
      {showConfigModel && (taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle') ? (
        <StatusPanel />
      ) : (
        <>
          <div className="mx-auto w-full">
            {/* Skip Button and Navigation Controls */}
            <div className="flex justify-end mb-2 -mt-5">
              <div className="flex space-x-2">
                <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-semantic-gray-100 transition border border-semantic-gray-200"
                  onClick={handleBackToInterfaceList}

                >
                  <ArrowLeft size={16} className="mr-2" />
                  Back to Interface List
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-semantic-gray-100 transition border border-semantic-gray-200 ${isFirstInterface ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handlePrevious}
                  disabled={isFirstInterface}
                >
                  <ArrowLeft size={16} className="mr-1" />
                  Previous
                </button>
                <button
                  className={`flex items-center bg-white text-black px-3 py-1 rounded hover:bg-semantic-gray-100 transition border border-semantic-gray-200 ${isLastInterface ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={handleNext}
                  disabled={isLastInterface}
                >
                  Next
                  <ArrowRight size={16} className="ml-1" />
                </button>
              </div>
              {/* <button
            className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-semantic-gray-200"
            onClick={handleSkipAll}
            disabled={interfaces.length === 0}
          >
            <FaForward size={16} className="mr-1" />
            Skip All
          </button> */}
            </div>

            {showAllSkippedNotification && renderAllInterfacesSkippedNotification()}

            {/* Interface Progress */}
            {!showAllSkippedNotification && interfaces.length > 0 && (
              <div className="flex justify-between items-center mb-6 px-4 py-2 bg-semantic-gray-100 rounded">
                <div className="text-semantic-gray-700 font-weight-medium">Interface Progress:</div>
                <div className="flex items-center space-x-2">
                  <span className="text-semantic-gray-700">{currentIndex + 1} / {interfaceCount}</span>
                  <div className="w-24 h-2 bg-semantic-gray-200 rounded-full">
                    <div
                      className="h-2 bg-primary rounded-full"
                      style={{ width: `${((currentIndex + 1) / interfaceCount) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            )}

            {/* Interface Card */}
            {currentInterfaceData ? (
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <div className="flex justify-between items-center mb-4 space-x-4">
                  <div className='flex items-center space-x-4 flex-wrap'>
                    <div className="typography-body-sm bg-semantic-gray-100 text-semantic-gray-500 px-3 py-1 rounded">
                      {currentInterfaceData && currentInterfaceData.child_node?.id
                        ? `INTERFACE-${currentInterfaceData.child_node?.id}`
                        : 'INTERFACE-ID ERROR: Missing ID'
                      }
                    </div>
                    <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800 ">
                      {currentInterfaceData.child_node?.properties?.Title ||
                        currentInterfaceData.properties?.Title ||
                        'Untitled Interface'}
                    </h2>
                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                      {currentInterfaceData.child_node?.type ||
                        currentInterfaceData.type ||
                        'Interface'}
                    </span>
                    <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full typography-body-sm">
                      {(currentInterfaceData.child_node?.properties?.InterfaceDescription ||
                        currentInterfaceData.properties?.InterfaceDescription) ?
                        'Has Description' : 'No Description'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {/* <span className={`px-3 py-1 rounded-full typography-body-sm ${interfaceDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS ? "bg-primary-100 text-primary-800" : "bg-purple-100 text-purple-800"
                  }`}>
                  {interfaceDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS ? "Design Details" : "Definition"}
                </span> */}
                    {/* <button
                  className="flex items-center bg-white text-black px-3 py-1 rounded hover:bg-primary-100 hover:text-primary-700 transition border border-semantic-gray-200"
                  onClick={handleSkip}
                >
                  <FaForward size={16} className="mr-1" />
                  {isInterfaceComplete(currentInterfaceData.id) ? 'Next Interface' : 'Skip'}
                </button> */}
                  </div>
                </div>



                <div className="flex space-x-2 mb-6">

                </div>

                {showSkipNotification ? (
                  renderInterfaceSkippedNotification()
                ) : isCurrentInterfaceDiscussionComplete() ? (
                  renderSuccessBadge(interfaceDiscussionType)
                ) : isInterfaceConfigured(currentInterfaceData) ? (
                  renderConfiguredInterface(currentInterfaceData)
                ) : (
                  <>
                    <div className="mb-6">
                      <div className='flex justify-between items-center mb-2'>
                        <h3 className="typography-body-lg font-weight-medium text-semantic-gray-700 mb-4">
                          Configure Interface {interfaceDiscussionType === DISCUSSION_TYPES.DESIGN_DETAILS ? "Design Details" : "Definition"}
                        </h3>
                        {/* Progress Bar aligned right */}
                        <div className="flex items-center space-x-2">
                          <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full typography-body-sm">
                            Discussion Progress
                          </span>
                          <span className="text-primary-700 typography-body-sm">
                            {currentDiscussionIndex + 1} / {DISCUSSION_TYPE.length}
                          </span>
                          <div className="w-24 h-2 bg-primary-100 rounded-full">
                            <div
                              className="h-2 bg-primary rounded-full transition-all duration-300"
                              style={{
                                width: `${((currentDiscussionIndex + 1) / DISCUSSION_TYPE.length) * 100}%`,
                              }}
                            ></div>
                          </div>
                        </div></div>
                      <div className="mt-4 p-4 bg-amber-100 border-l-4 border-amber-400 rounded-md flex items-center">
                        <AlertCircle className="text-amber-600 mr-3" size={24} />
                        <p className="text-amber-800 typography-body-sm font-weight-medium">
                          To view the Interface details, make sure both the Interface Design and Interface Definition are completed
                        </p>
                      </div>

                    </div>
                    <div className="flex flex-wrap gap-2 mb-6">
                      {DISCUSSION_TYPE.map((type, index) => {
                        const interfaceId = currentInterfaceData.child_node?.id;
                        const isCompleted = isDiscussionCompleted(interfaceId, type);
                        // First discussion is always selectable, second only if first is completed
                        const isSelectable = index === 0 || isDiscussionCompleted(interfaceId, DISCUSSION_TYPE[0]);
                        const isDisabled = (index === 0 && isCompleted) ||
                          (index === 1 && (!isDiscussionCompleted(interfaceId, DISCUSSION_TYPES[0]) || isCompleted));


                        return (
                          <button
                            key={type}
                            onClick={() => {
                              if (!isDisabled) {
                                setCurrentDiscussionIndex(index);
                              }
                            }}
                            className={`px-3 py-1 rounded-full typography-body-sm flex items-center gap-1 
              ${index === currentDiscussionIndex
                                ? 'bg-primary-100 text-primary-800 border border-primary-300'
                                : isCompleted
                                  ? 'bg-green-100 text-green-800 cursor-pointer'
                                  : isSelectable
                                    ? 'bg-semantic-gray-100 text-semantic-gray-600 cursor-pointer hover:bg-semantic-gray-200'
                                    : 'bg-semantic-gray-100 text-semantic-gray-400 opacity-60 cursor-not-allowed'
                              }`}
                            disabled={isDisabled}
                            title={
                              isCompleted
                                ? "This discussion is already completed"
                                : index === 1 && !isDiscussionCompleted(interfaceId, DISCUSSION_TYPES[0])
                                  ? "Complete Interface Design discussion first"
                                  : ""
                            }
                          >
                            {index + 1}. {getDiscussionTitle(type)}
                            {isCompleted && (
                              <CheckCircle size={14} className="text-green-600 ml-1" />
                            )}
                          </button>
                        );
                      })}
                    </div>


                    {/* Configuration Buttons */}
                    <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                      <div
                        className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'discussion' ? 'border-primary bg-primary-50' : 'border-semantic-gray-200'
                          } ${currentDiscussionIndex === 1 && !isDiscussionCompleted(currentInterfaceData.child_node?.id, "design_details") ? 'opacity-75 cursor-not-allowed pointer-events-none' : ''}`}
                        onClick={() => handleUpdateInterface(currentInterfaceData.child_node?.id)}
                      >
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="py-1 px-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                            <Image
                              src={Logo}
                              alt="Logo"
                              width={16}
                              height={16}
                              className="text-primary"
                            />
                          </div>
                          <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                        </div>

                        <p className="text-semantic-gray-600">
                          {en.InterfaceUpdate}
                        </p>

                        {isDiscussionCompleted(currentInterfaceData.child_node?.id, currentDiscussionType) && (
                          <div className="mt-4 flex items-center text-green-700">
                            <CheckCircle size={16} className="mr-1" />
                            Completed
                          </div>
                        )}
                      </div>

                      <div
                        className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === 'auto' ? 'border-primary bg-primary-50' : 'border-semantic-gray-200'
                          }`}
                        onClick={handleConfigureClick}
                      >
                        <div className="flex mb-4 items-center space-x-2">
                          <div className="p-1.5 bg-primary-100 rounded-lg flex items-center justify-center">
                            <Upload className="w-4 h-4 text-primary" />
                          </div>
                          <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                        </div>

                        <p className="text-semantic-gray-600">
                          Let our LLM automatically configure this interface based on the available information
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ) : interfaces.length === 0 && !showAllSkippedNotification ? (
              <div className="text-center">
                <p className="text-semantic-gray-500 my-4">No interfaces found for this project.</p>
              </div>
            ) : null}
          </div></>)}

      {configureModel && currentInterfaceData && (
        <ConfigureModal
          id={currentInterfaceData.id}
          type={"Architecture"}
          isNodeType={"Architecture"}
          isCreateProject={true}
          setShowConfigModel={setShowConfigModel}
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={handleConfigureSuccess}
        />
      )}
    </div>
  );
}