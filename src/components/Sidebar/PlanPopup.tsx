import React, { useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/navigation';
import OrangeStar from '@/../public/images/payment/orange_star.svg';
import Image from 'next/image';
import { useUser } from '@/components/Context/UserContext';

interface SubscriptionData {
  currentPlan: string;
  planCredits: number;
  organizationCost: number;
}

interface PlanPopupProps {
  isOpen: boolean;
  onClose: () => void;
  credits: number;
  isSubscriptionLoading: boolean;
  subscriptionData: SubscriptionData;
  containerRef: React.RefObject<HTMLDivElement>;
}

const PlanPopup: React.FC<PlanPopupProps> = ({
  isOpen,
  onClose,
  credits,
  isSubscriptionLoading,
  subscriptionData,
  containerRef
}) => {
  const { user_id } = useUser();
  const popupRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const [currentPlan, setCurrentPlan] = useState<string>('Loading...');
  const [planCredits, setPlanCredits] = useState<number>(0);
  const [organizationCost, setOrganizationCost] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);

  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node) &&
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose, containerRef]);

  useEffect(() => {
    if (subscriptionData) {
      setCurrentPlan(subscriptionData.currentPlan);
      setPlanCredits(subscriptionData.planCredits);
      setOrganizationCost(subscriptionData.organizationCost);
    }
    
    if (isSubscriptionLoading !== undefined) {
      setIsLoading(isSubscriptionLoading);
    }
  }, [subscriptionData, isSubscriptionLoading]);

  if (!isOpen) return null;

  return (
    <div
      ref={popupRef}
      className="fixed left-[72px] bottom-0 transform -translate-y-1/2 w-64 bg-white rounded-lg shadow-lg p-4 border border-semantic-gray-200 z-[99999]"
    >
      <div className="flex justify-between items-center mb-4">
        <div className="flex flex-col">
          <span className="text-semantic-gray-700 font-weight-medium">{currentPlan}</span>
          {!isLoading && currentPlan === 'Free' && (
            <span className="typography-caption text-semantic-gray-500">Upgrade for more features</span>
          )}
        </div>
        <button
          onClick={() => router.push('/pricing')}
          className="px-3 py-1 typography-body-sm text-primary bg-semantic-gray-100 rounded-md border border-semantic-gray-200 hover:text-primary-600 font-weight-light"
        
        >
          Upgrade
        </button>
      </div>
      <div className="border-b border-dashed border-semantic-gray-400 my-4"></div>
      <div className="flex items-center justify-between gap-2 text-semantic-gray-600">
        <span>Remaining Credits</span>
        <div className="flex items-center gap-1">
          <Image src={OrangeStar} alt="Credits" width={12} height={12} />
          <span className="ml-auto font-weight-medium">
          {planCredits == 0 ? "Loading" : (planCredits - organizationCost * 20000) > 0 ? Math.round(planCredits - organizationCost * 20000) : 0}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PlanPopup;