import React, { useEffect, useState, useMemo } from "react";
import { getCookie } from "@/utils/auth";
import { getAssignedTasks, getOrganizationNameById } from "@/utils/api";
import Cookies from "js-cookie";
import { Building2 } from "lucide-react";

const Header = () => {
  const [user, setUser] = useState({ username: "", email: "" });
  const [taskValue, setTaskValue] = useState(0);
  const [organizationName, setOrganizationName] = useState("");
  const organizationId = Cookies.get("tenant_id");

  useEffect(() => {
    const fetchOrganizationName = async () => {
      try {
        if (organizationId) {
          const orgData = await getOrganizationNameById(organizationId);
          setOrganizationName(orgData.name);
        }
      } catch (error) {
        
      }
    };
    
    fetchOrganizationName();
  }, [organizationId]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [usernameFromLocalStorage, email, usernameFromCookie] = await Promise.all([
          localStorage.getItem("username"),
          getCookie("email"),
          getCookie("username"),
        ]);

        setUser({
          username: usernameFromLocalStorage || usernameFromCookie,
          email,
        });

        const tasks = await getAssignedTasks();
        setTaskValue(tasks.length);
      } catch (error) {
        
      }
    };

    fetchData();
  }, []);

  const greetingMessage = useMemo(() => {
    const currentHour = new Date().getHours();
    if (currentHour < 12) return "Good morning";
    if (currentHour < 18) return "Good afternoon";
    return "Good evening";
  }, []);

  return (
    <div className="home-header-container">
      <div className="container mx-auto">
        <div className="greetings-text flex items-center">
          <span >
            {greetingMessage}, {user.username}
          </span>
          {
            organizationName && (
              <span className="gradient-text flex items-center">
                &nbsp; - <Building2 className="w-6 h-6 text-semantic-gray-600 mx-2" /> {organizationName}
              </span>
            )
          }
        </div>
        <div className="info-section">
          <div className="info-title">
            Here&apos;s your briefing for today:
          </div>
          <p className="info-text">
            You have{" "}
            <span className="task-badge">
              {taskValue} {taskValue === 1 ? "TASK" : "TASKS"}
            </span>{" "}
            scheduled for <span className="font-weight-bold">today.</span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Header;
