import React, { useContext } from "react";
import { FaChevronRight } from "react-icons/fa";
import { useRouter } from "next/navigation";
import {fetchNodeById } from "@/utils/api";
import { formatUTCToLocal } from "@/utils/datetime";
import { TopBarContext } from "@/components/Context/TopBarContext";

const ActivityCard = ({
  title,
  date,
  projectId,
  onProjectClick,
  className,
}) => (
  <div
    className={`activity-card ${className}`}
    onClick={() => onProjectClick(projectId, title)}
  >
    <div className="activity-card-content">
      <div className="activity-card-title">{title}</div>
      <div className="activity-card-date">{date}</div>
    </div>
    <FaChevronRight className="activity-card-chevron" />
  </div>
);

const RecentActivityList = ({ activities }) => {
  const router = useRouter();
  const { tabs, addTab, setActiveTab } = useContext(TopBarContext);

  const handleProjectClick = async (projectId, title) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    const taskUrl = buildProjectUrl(projectId, 'overview');

    const projectRes = await fetchNodeById(projectId, "Project");
    let titleVal = projectRes.properties?.Title || projectRes.properties?.Name || "Project"

    const existingTab = tabs.find((tab) => tab.href.includes(projectId));

    if (existingTab) {
      setActiveTab(existingTab.id);
      router.push(existingTab.href !== taskUrl ? existingTab.href : taskUrl);
    } else {
      addTab(titleVal, taskUrl);
      router.push(taskUrl);
    }
  };

  if (activities.length === 0) return (
    <div className="text-semantic-gray-500 text-center">
      {/* No Recent Activity available */}
    </div>
  )


  return (
    <>
      <h5 className="recent-activity-title">Latest Activity</h5>
      <div className="recent-activity-list">
        {activities.map((activity, index) => (
          <ActivityCard
            key={activity.project_id}
            title={`Activity of ${activity.project_name}`}
            date={formatUTCToLocal(activity.timestamp)}
            projectId={activity.project_id}
            onProjectClick={handleProjectClick}
            className={
              index === 0
                ? "activity-card-first"
                : index === activities.length - 1
                ? "activity-card-last"
                : "activity-card-middle"
            }
          />
        ))}
      </div>
    </>
  );
};

export default RecentActivityList;
