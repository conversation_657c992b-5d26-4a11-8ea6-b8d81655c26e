"use client";
import React, { useState } from 'react';
import Link from 'next/link';
import { Shield, Rocket } from 'lucide-react';

const TermsAcceptanceModal = ({ onAccept, onLogout }) => {
  const [accepted, setAccepted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-5 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
        {/* Illustration */}
        <div className="text-center mb-8">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
            isSuccess 
              ? 'bg-[hsl(var(--primary))] bg-opacity-20' 
              : 'bg-[hsl(var(--primary))] bg-opacity-10'
          }`}>
            {isSuccess ? (
              <Rocket className="w-8 h-8 text-[hsl(var(--primary))] animate-bounce" />
            ) : (
              <Shield className="w-8 h-8 text-[hsl(var(--primary))]" />
            )}
          </div>
          <h2 className="text-2xl font-semibold text-semantic-gray-900 mb-2">
            {isSuccess ? 'Welcome to Kavia!' : 'Terms of Service'}
          </h2>
          <p className="text-semantic-gray-600">
            {isSuccess 
              ? 'Terms accepted! You\'re now ready to start building amazing projects.' 
              : 'To continue using Kavia, please accept our terms of service'
            }
          </p>
        </div>

        {/* Checkbox - Hide when success */}
        {!isSuccess && (
          <div className="mb-6">
            <label className="flex items-start space-x-3">
              <input
                type="checkbox"
                checked={accepted}
                onChange={(e) => {
                  setAccepted(e.target.checked);
                  if (error) setError('');
                }}
                className="mt-1 rounded border-semantic-gray-300 text-[hsl(var(--primary))] focus:ring-[hsl(var(--primary))]"
              />
              <span className="text-sm text-semantic-gray-700">
                I have read and agreed to the{" "}
                <Link 
                  href="https://kavia.ai/terms" 
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[hsl(var(--primary))] hover:underline"
                >
                  terms of service
                </Link>
              </span>
            </label>
          </div>
        )}

        {/* Error message */}
        {error && !isSuccess && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Buttons - Hide when success */}
        {!isSuccess && (
          <div className="flex flex-col space-y-3">
          <button
            onClick={async () => {
              setIsLoading(true);
              setError('');
              try {
                await onAccept();
                setIsSuccess(true);
                // Auto-close modal after showing success message
                setTimeout(() => {
                  window.location.reload();
                }, 2000);
              } catch (err) {
                console.error('Terms acceptance failed:', err);
                setError('Failed to accept terms. Please check your connection and try again.');
              } finally {
                setIsLoading(false);
              }
            }}
            disabled={!accepted || isLoading}
            className={`w-full py-3 px-4 rounded-md text-white font-medium transition-colors flex items-center justify-center ${
              accepted && !isLoading
                ? 'bg-[hsl(var(--primary))] hover:bg-[#E55A0B]'
                : 'bg-semantic-gray-300 cursor-not-allowed'
            }`}
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Accepting...
              </>
            ) : (
              'Accept and Continue'
            )}
          </button>
          <button
            onClick={onLogout}
            disabled={isLoading}
            className={`w-full py-2 px-4 rounded-md font-medium border border-semantic-gray-300 transition-colors ${
              isLoading 
                ? 'text-semantic-gray-400 cursor-not-allowed bg-semantic-gray-50' 
                : 'text-semantic-gray-700 hover:bg-semantic-gray-50'
            }`}
          >
            Logout
          </button>
        </div>
        )}
      </div>
    </div>
  );
};

export default TermsAcceptanceModal; 