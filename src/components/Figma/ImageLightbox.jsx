// ImageLightbox.jsx
import React, { useState, useCallback, useRef, useEffect } from 'react';
import Image from 'next/image';
import {
  X,
  ZoomIn,
  ZoomOut,
  Download,
  Maximize,
  Minimize,
  RotateCw,
  RotateCcw,
  Expand,
  ScanLine,
  ImageOff,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Info
} from 'lucide-react';

const ImageLightbox = ({ imageUrl, onClose, images = [], initialIndex = 0 }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  const [rotation, setRotation] = useState(0);
  const [fitToScreen, setFitToScreen] = useState(true);
  const [showInfo, setShowInfo] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(initialIndex);
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [slideshowInterval, setSlideshowInterval] = useState(null);
  const [loadError, setLoadError] = useState(false);

  const imageContainerRef = useRef(null);
  const imageRef = useRef(null);

  // Determine the current image URL
  const currentImageUrl = images.length > 0 ? images[currentImageIndex] : imageUrl;
  // Reset position when scale changes
  useEffect(() => {
    setPosition({ x: 0, y: 0 });
  }, [scale]);

  // Calculate best fit scale when image dimensions change
  useEffect(() => {
    if (fitToScreen && imageDimensions.width > 0 && imageDimensions.height > 0 && imageContainerRef.current) {
      const container = imageContainerRef.current;
      const containerWidth = container.clientWidth - 48; // Account for padding
      const containerHeight = container.clientHeight - 48;

      const scaleX = containerWidth / imageDimensions.width;
      const scaleY = containerHeight / imageDimensions.height;

      // Use the smaller scale to ensure the entire image fits
      const newScale = Math.min(scaleX, scaleY, 1); // Don't scale up images that are smaller than the container

      setScale(newScale);
    }
  }, [imageDimensions, fitToScreen]);

  // Store image dimensions once loaded
  const handleImageLoad = useCallback((img) => {
    setIsLoading(false);
    setLoadError(false);
    setImageDimensions({
      width: img.naturalWidth,
      height: img.naturalHeight
    });
  }, []);

  // Handle image load error
  const handleImageError = useCallback(() => {
    setIsLoading(false);
    setLoadError(true);
  }, []);

  // Rotation handlers
  const handleRotateClockwise = useCallback((e) => {
    e.stopPropagation();
    setRotation(prev => (prev + 90) % 360);
  }, []);

  const handleRotateCounterClockwise = useCallback((e) => {
    e.stopPropagation();
    setRotation(prev => (prev - 90 + 360) % 360);
  }, []);

  // Toggle fit to screen
  const toggleFitToScreen = useCallback((e) => {
    e.stopPropagation();
    setFitToScreen(prev => !prev);
    setScale(fitToScreen ? 1 : Math.min(
      imageContainerRef.current.clientWidth / imageDimensions.width,
      imageContainerRef.current.clientHeight / imageDimensions.height
    ));
  }, [fitToScreen, imageDimensions]);

  // Calculate minimum scale based on image dimensions and container size
  const calculateMinScale = useCallback(() => {
    return 0.3; // Fixed 30% minimum scale
  }, []);

  const handleZoomIn = useCallback((e) => {
    e.stopPropagation();
    setScale(prev => Math.min(prev + 0.25, 3));
  }, []);

  const handleZoomOut = useCallback((e) => {
    e.stopPropagation();
    const minScale = calculateMinScale();
    setScale(prev => Math.max(prev - 0.25, minScale));
  }, [calculateMinScale]);

  const handleDownload = useCallback(async (e) => {
    e.stopPropagation();
    try {
      // Create blob from image URL
      const response = await fetch(currentImageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = 'image-download.png';
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      
    }
  }, [imageUrl]);

  const handleBackdropClick = useCallback((e) => {
    if (e.target === e.currentTarget) {
      if (document.fullscreenElement) {
        document.exitFullscreen().then(() => {
          onClose();
        }).catch(err => {
          
          onClose();
        });
      } else {
        onClose();
      }
    }
  }, [onClose]);

  const toggleFullscreen = useCallback((e) => {
    e.stopPropagation();
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        
      });
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
        setIsFullscreen(false);
      }
    }
  }, []);

  // Mouse drag functionality for panning the image
  const handleMouseDown = useCallback((e) => {
    if (scale > 1) {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
    }
  }, [scale, position]);

  const handleMouseMove = useCallback((e) => {
    if (isDragging && scale > 1) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      // Optional: Add boundaries to prevent dragging too far
      // This is a simplified version - for production you might want to calculate based on container size
      const maxX = (imageDimensions.width * scale - (imageContainerRef.current?.clientWidth || 0)) / 2;
      const maxY = (imageDimensions.height * scale - (imageContainerRef.current?.clientHeight || 0)) / 2;

      const boundedX = Math.max(-maxX, Math.min(maxX, newX));
      const boundedY = Math.max(-maxY, Math.min(maxY, newY));

      setPosition({ x: boundedX, y: boundedY });
    }
  }, [isDragging, dragStart, scale, imageDimensions]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Touch functionality for mobile devices
  const handleTouchStart = useCallback((e) => {
    if (scale > 1) {
      const touch = e.touches[0];
      setIsDragging(true);
      setDragStart({ x: touch.clientX - position.x, y: touch.clientY - position.y });
    }
  }, [scale, position]);

  const handleTouchMove = useCallback((e) => {
    if (isDragging && scale > 1) {
      const touch = e.touches[0];
      const newX = touch.clientX - dragStart.x;
      const newY = touch.clientY - dragStart.y;
      setPosition({ x: newX, y: newY });
      e.preventDefault(); // Prevent scrolling while dragging
    }
  }, [isDragging, dragStart, scale]);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle mouse wheel zoom
  const handleWheel = useCallback((e) => {
    e.preventDefault();

    // Calculate zoom step based on current scale
    // Smaller steps when zoomed out, larger steps when zoomed in
    const zoomStep = Math.max(0.05, scale * 0.1);

    if (e.deltaY < 0) {
      // Zoom in
      setScale(prev => Math.min(prev + zoomStep, 3));
    } else {
      // Zoom out
      const minScale = calculateMinScale();
      setScale(prev => Math.max(prev - zoomStep, minScale));
    }
  }, [scale, calculateMinScale]);

  // Add wheel event listener
  useEffect(() => {
    const container = imageContainerRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }
    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, [handleWheel]);

  // Clean up slideshow interval on unmount
  useEffect(() => {
    return () => {
      if (slideshowInterval) {
        clearInterval(slideshowInterval);
      }
    };
  }, [slideshowInterval]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      switch(e.key) {
        case 'Escape':
          // Let the browser handle exiting fullscreen on ESC
          // and then call onClose after a short delay
          if (document.fullscreenElement) {
            setTimeout(onClose, 100);
          } else {
            onClose();
          }
          break;
        case '+':
          if (scale < 3) setScale(prev => Math.min(prev + 0.25, 3));
          break;
        case '-':
          const minScale = calculateMinScale();
          if (scale > minScale) setScale(prev => Math.max(prev - 0.25, minScale));
          break;
        case 'f':
          toggleFullscreen(e);
          break;
        case 'r':
          handleRotateClockwise(e);
          break;
        case 'R':
          handleRotateCounterClockwise(e);
          break;
        case 'i':
          setShowInfo(prev => !prev);
          break;
        case 'ArrowLeft':
          if (images.length > 1) {
            setCurrentImageIndex(prev => (prev - 1 + images.length) % images.length);
            setIsLoading(true);
            setPosition({ x: 0, y: 0 });
            setRotation(0);
          }
          break;
        case 'ArrowRight':
          if (images.length > 1) {
            setCurrentImageIndex(prev => (prev + 1) % images.length);
            setIsLoading(true);
            setPosition({ x: 0, y: 0 });
            setRotation(0);
          }
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose, scale, toggleFullscreen, handleRotateClockwise, handleRotateCounterClockwise, images.length, calculateMinScale]);

  // Clean up event listeners
  useEffect(() => {
    const handleGlobalMouseUp = () => setIsDragging(false);
    window.addEventListener('mouseup', handleGlobalMouseUp);
    window.addEventListener('touchend', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
      window.removeEventListener('touchend', handleGlobalMouseUp);
    };
  }, []);

  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Add useEffect for cleanup on unmount
  useEffect(() => {
    return () => {
      // Ensure we exit fullscreen when component unmounts
      if (document.fullscreenElement) {
        document.exitFullscreen().catch(err =>
          {});
      }
    };
  }, []);

  return (
    <div
      className="fixed inset-0 bg-black/90 backdrop-blur-sm flex flex-col z-50"
      onClick={handleBackdropClick}
    >
      {/* Fixed header */}
      <div
        className="flex items-center justify-between px-6 h-16 bg-white/10 backdrop-blur-md z-10"
        onClick={e => e.stopPropagation()}
      >
        <h3 className="text-white font-weight-medium">Frame Preview</h3>
        <div className="flex items-center gap-4">
          <div className="flex items-center bg-white/10 rounded-lg p-1">
            <button
              onClick={handleZoomOut}
              className="p-2 rounded-md text-white hover:bg-white/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={scale <= calculateMinScale()}
              aria-label="Zoom out"
              title={`Zoom out (min: ${Math.round(calculateMinScale() * 100)}%)`}
            >
              <ZoomOut size={18} />
            </button>
            <span className="px-3 text-white font-weight-medium min-w-16 text-center">
              {Math.round(scale * 100)}%
            </span>
            <button
              onClick={handleZoomIn}
              className="p-2 rounded-md text-white hover:bg-white/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={scale >= 3}
              aria-label="Zoom in"
            >
              <ZoomIn size={18} />
            </button>
          </div>

          <button
            onClick={handleDownload}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label="Download image"
            title="Download image"
          >
            <Download size={20} />
          </button>

          <button
            onClick={toggleFullscreen}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
          >
            {isFullscreen ? <Minimize size={20} /> : <Maximize size={20} />}
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              if (document.fullscreenElement) {
                document.exitFullscreen().then(() => {
                  onClose();
                }).catch(err => {
                  
                  onClose();
                });
              } else {
                onClose();
              }
            }}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label="Close preview"
            title="Close preview"
          >
            <X size={20} />
          </button>
        </div>
      </div>

      {/* Content area - takes remaining height */}
      <div
        ref={imageContainerRef}
        className="flex-1 overflow-hidden p-6 select-none flex items-center justify-center"
        onClick={e => e.stopPropagation()}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{ cursor: scale > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
      >
        {isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/50 backdrop-blur-sm z-10">
            <div className="w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mb-4"></div>
            <p className="text-white/80 typography-body-sm animate-pulse">Loading image...</p>
          </div>
        )}

        <div className="w-full h-full flex items-center justify-center">
          <div
            className="transform-gpu transition-transform"
            style={{
              transform: `scale(${scale})`,
              transformOrigin: 'center center',
              transitionProperty: isDragging ? 'none' : 'transform',
              transitionDuration: '0.2s',
              transitionTimingFunction: 'ease-out',
            }}
          >
            <div
              style={{
                transform: `translate(${position.x / scale}px, ${position.y / scale}px) rotate(${rotation}deg)`,
                transition: isDragging ? 'none' : 'transform 0.2s ease-out',
              }}
            >
              {loadError ? (
                <div className="flex flex-col items-center justify-center p-8 bg-semantic-gray-800 rounded-lg">
                  <ImageOff size={48} className="text-semantic-gray-400 mb-4" />
                  <p className="text-semantic-gray-300 text-center">Failed to load image</p>
                </div>
              ) : (
                <Image
                  ref={imageRef}
                  src={currentImageUrl}
                  alt="Frame preview"
                  width={imageDimensions.width || 800}
                  height={imageDimensions.height || 600}
                  className="rounded-lg max-w-none max-h-none"
                  onLoadingComplete={(img) => handleImageLoad(img)}
                  onError={handleImageError}
                  quality={90}
                  priority
                  unoptimized={true}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom toolbar */}
      <div className="flex items-center justify-center p-4 bg-white/10 backdrop-blur-md">
        <div className="flex items-center gap-2">
          {/* Rotation controls */}
          <button
            onClick={handleRotateCounterClockwise}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label="Rotate counter-clockwise"
            title="Rotate counter-clockwise"
          >
            <RotateCcw size={20} />
          </button>

          <button
            onClick={handleRotateClockwise}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label="Rotate clockwise"
            title="Rotate clockwise"
          >
            <RotateCw size={20} />
          </button>

          {/* Fit to screen toggle */}
          <button
            onClick={toggleFitToScreen}
            className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
            aria-label={fitToScreen ? "Actual size" : "Fit to screen"}
            title={fitToScreen ? "Actual size" : "Fit to screen"}
          >
            {fitToScreen ? <Expand size={20} /> : <ScanLine size={20} />}
          </button>

          {/* Image info toggle */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowInfo(prev => !prev);
            }}
            className={`p-2 rounded-md text-white hover:bg-white/20 transition-colors ${showInfo ? 'bg-white/20' : ''}`}
            aria-label="Image information"
            title="Image information"
          >
            <Info size={20} />
          </button>

          {/* Navigation controls (only show if we have multiple images) */}
          {images.length > 1 && (
            <>
              <div className="h-6 border-l border-white/20 mx-2"></div>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(prev => (prev - 1 + images.length) % images.length);
                  setIsLoading(true);
                  setPosition({ x: 0, y: 0 });
                  setRotation(0);
                }}
                className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
                aria-label="Previous image"
                title="Previous image"
                disabled={images.length <= 1}
              >
                <ChevronLeft size={20} />
              </button>

              <span className="text-white/80 typography-body-sm px-2">
                {currentImageIndex + 1} / {images.length}
              </span>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setCurrentImageIndex(prev => (prev + 1) % images.length);
                  setIsLoading(true);
                  setPosition({ x: 0, y: 0 });
                  setRotation(0);
                }}
                className="p-2 rounded-md text-white hover:bg-white/20 transition-colors"
                aria-label="Next image"
                title="Next image"
                disabled={images.length <= 1}
              >
                <ChevronRight size={20} />
              </button>

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (isSlideshow) {
                    clearInterval(slideshowInterval);
                    setSlideshowInterval(null);
                    setIsSlideshow(false);
                  } else {
                    const interval = setInterval(() => {
                      setCurrentImageIndex(prev => (prev + 1) % images.length);
                      setIsLoading(true);
                      setPosition({ x: 0, y: 0 });
                      setRotation(0);
                    }, 3000);
                    setSlideshowInterval(interval);
                    setIsSlideshow(true);
                  }
                }}
                className={`p-2 rounded-md text-white hover:bg-white/20 transition-colors ${isSlideshow ? 'bg-white/20' : ''}`}
                aria-label={isSlideshow ? "Stop slideshow" : "Start slideshow"}
                title={isSlideshow ? "Stop slideshow" : "Start slideshow"}
                disabled={images.length <= 1}
              >
                {isSlideshow ? <Pause size={20} /> : <Play size={20} />}
              </button>
            </>
          )}
        </div>
      </div>

      {/* Image information overlay */}
      {showInfo && !isLoading && !loadError && (
        <div className="absolute bottom-20 left-4 bg-black/80 backdrop-blur-sm p-4 rounded-lg text-white typography-body-sm">
          <p><strong>Dimensions:</strong> {imageDimensions.width} × {imageDimensions.height}px</p>
          <p><strong>Scale:</strong> {Math.round(scale * 100)}% (min: {Math.round(calculateMinScale() * 100)}%, max: 300%)</p>
          <p><strong>Rotation:</strong> {rotation}°</p>
          {imageDimensions.width > 0 && (
            <p className="text-semantic-gray-400 typography-caption mt-2">
              {imageDimensions.width * imageDimensions.height > 1000000 ?
                "Large image detected - enhanced zoom out enabled" : ""}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default ImageLightbox;