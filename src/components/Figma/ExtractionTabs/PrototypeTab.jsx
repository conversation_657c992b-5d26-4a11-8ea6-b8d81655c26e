'use client'
import { useState, useEffect, useRef } from 'react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import {
  Maximize2,
  ZoomIn,
  ZoomOut,
  Move,
  SplitSquareVertical,
  SlidersHorizontal,
  Eye,
  Code,
  Minimize2
} from "lucide-react";
import CodeViewer from '@/components/Sandbox/Sandbox';
import { ReactCompareSlider, ReactCompareSliderImage } from 'react-compare-slider';

const PrototypeTab = ({ projectId, selectedDesign, selectedFrame, generatedHtml }) => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isComparing, setIsComparing] = useState(false);
  const [comparisonMode, setComparisonMode] = useState('sideBySide'); // 'slider' or 'sideBySide'
  const [zoomLevel, setZoomLevel] = useState(100);
  const [viewMode, setViewMode] = useState('design'); // 'design', 'code', or 'compare'
  const codeViewerRef = useRef(null);
  const containerRef = useRef(null);

  // If generatedHtml is provided as prop, use it; otherwise, use outputHtml state
  const [outputHtml, setOutputHtml] = useState('');
  const htmlContent = generatedHtml || outputHtml;

  // We'll use the selectedFrame passed from the parent component
  const frame = selectedFrame || {
    id: 5,
    name: 'Morse Code Converter',
    original: '/api/placeholder/800/600',
    dimensions: {
      width: 1440,
      height: 900
    }
  };

  // Generate HTML output for comparison when frame changes
  useEffect(() => {

    if (selectedFrame && !generatedHtml) {
      generateHtmlOutput(selectedFrame);
    }

    // Calculate initial zoom when frame changes
    if (selectedFrame) {
      calculateInitialZoom();
    }
  }, [selectedFrame, generatedHtml]);

  // Calculate initial zoom level based on frame dimensions and container size
  const calculateInitialZoom = () => {
    // Wait for next render cycle to ensure container is available
    setTimeout(() => {
      if (!containerRef.current || !frame.dimensions) return;

      const containerWidth = containerRef.current.clientWidth - 48; // Subtract padding
      const containerHeight =
        (containerRef.current.clientHeight || window.innerHeight * 0.7) - 48; // Subtract padding

      const frameWidth = frame.dimensions.width;
      const frameHeight = frame.dimensions.height;

      // Calculate optimal fit
      const widthRatio = containerWidth / frameWidth;
      const heightRatio = containerHeight / frameHeight;

      // Use the smaller ratio to ensure image fits in both dimensions
      // Convert to percentage (50-200 range)
      let optimalZoom = Math.min(widthRatio, heightRatio) * 100;

      // Clamp zoom level between 50% and 200%
      optimalZoom = Math.max(50, Math.min(200, optimalZoom));

      // Round to nearest 25% increment for cleaner UI (50, 75, 100, 125, etc.)
      optimalZoom = Math.round(optimalZoom / 25) * 25;

      // Set the zoom level
      setZoomLevel(optimalZoom);
    }, 100);
  };

  // Generate HTML output for comparison (simulated)
  const generateHtmlOutput = async (frame) => {
    // In a real implementation, you would fetch this from your API
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${frame.name}</title>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      margin: 0;
      padding: 0;
      color: #333;
      width: 100%;
      display: flex;
      justify-content: center;
    }
    .container {
      width: ${frame.dimensions.width}px;
      max-width: 100%;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #eee;
    }
    .navigation ul {
      display: flex;
      list-style: none;
      padding: 0;
      margin: 0;
    }
    .navigation li {
      margin-left: 24px;
    }
    .navigation a {
      text-decoration: none;
      color: #0066ff;
    }
    .hero {
      padding: 48px 0;
      text-align: center;
    }
    .primary-button {
      background-color: #0066ff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-weight: 600;
      cursor: pointer;
    }
    .features {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
      margin: 48px 0;
    }
    .feature-card {
      padding: 24px;
      border-radius: 8px;
      background-color: #f8f9fa;
      box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .footer {
      text-align: center;
      padding: 24px 0;
      border-top: 1px solid #eee;
      margin-top: 48px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>${frame.name}</h1>
      <nav class="navigation">
        <ul>
          <li><a href="#home">Home</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
      </nav>
    </header>

    <main class="content">
      <section class="hero">
        <h2>Welcome to ${frame.name}</h2>
        <p>This is an example component generated from your Figma design.</p>
        <button class="primary-button">Get Started</button>
      </section>

      <section class="features">
        <div class="feature-card">
          <h3>Feature 1</h3>
          <p>Description of the first feature</p>
        </div>
        <div class="feature-card">
          <h3>Feature 2</h3>
          <p>Description of the second feature</p>
        </div>
        <div class="feature-card">
          <h3>Feature 3</h3>
          <p>Description of the third feature</p>
        </div>
      </section>
    </main>

    <footer class="footer">
      <p>&copy; 2025 ${frame.name} - All rights reserved</p>
    </footer>
  </div>
</body>
</html>`;

    setOutputHtml(html);
  };

  // Get Figma image URL (placeholder)
  const getFigmaImageUrl = (frame) => {
    // This is a placeholder. In a real app, you would use the actual Figma image URL
    if (frame.imageUrl) return frame.imageUrl;

    // Use the original property if available
    if (frame.original) return frame.original;

    // Use placeholder image service with appropriate dimensions
    const width = frame.width || frame.dimensions?.width || 800;
    const height = frame.height || frame.dimensions?.height || 600;
    return `/api/placeholder/${width}/${height}?text=${encodeURIComponent(frame.name)}`;
  };

  const toggleFullScreen = () => {
    const newFullscreenState = !isFullScreen;
    setIsFullScreen(newFullscreenState);

    // Recalculate optimal zoom after toggling fullscreen
    // Add slight delay to ensure the DOM has updated
    setTimeout(() => {
      calculateInitialZoom();
    }, 200);
  };

  const setViewModeAndGenerateIfNeeded = (mode) => {
    // Initialize HTML content if necessary when entering code or compare modes
    if ((mode === 'code' || mode === 'compare') && !outputHtml && !generatedHtml && selectedFrame) {
      generateHtmlOutput(selectedFrame);
    }

    // Set the view mode
    setViewMode(mode);

    // Update comparison state based on the view mode
    setIsComparing(mode === 'compare');
  };

  const toggleComparisonMode = () => {
    setComparisonMode(prev => prev === 'slider' ? 'sideBySide' : 'slider');
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => {
      const newZoom = Math.min(prev + 25, 200);
      return newZoom;
    });
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => {
      const newZoom = Math.max(prev - 25, 50);
      return newZoom;
    });
  };

  const handleResetZoom = () => {
    setZoomLevel(100);
  };

  // Handle escape key for exiting fullscreen
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'Escape' && isFullScreen) {
        setIsFullScreen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullScreen]);

  // Handle window resize events to recalculate optimal zoom
  useEffect(() => {
    const handleResize = () => {
      calculateInitialZoom();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate initial zoom on component mount
  useEffect(() => {
    calculateInitialZoom();
  }, []);

  // Determine if the current frame is a mobile design
  const isMobileDesign = () => {
    return frame.dimensions?.width <= 375;
  };

  // Calculate the optimal sizing to fit the design in the viewport
  const calculateOptimalSize = () => {
    const maxViewportWidth = window.innerWidth * 0.9; // 90% of viewport width
    const maxViewportHeight = window.innerHeight * 0.7; // 70% of viewport height

    // Base dimensions
    const baseWidth = frame.dimensions?.width || 800;
    const baseHeight = frame.dimensions?.height || 600;

    // Apply zoom level to both fullscreen and non-fullscreen modes
    const zoomFactor = zoomLevel / 100;

    // For non-fullscreen mode, calculate a scaling factor
    if (!isFullScreen) {
      // For mobile designs, we want to display at actual size when possible
      if (isMobileDesign()) {
        return {
          width: baseWidth * zoomFactor,
          height: baseHeight * zoomFactor
        };
      }

      // For larger designs, scale to fit
      const widthScale = maxViewportWidth / (baseWidth * zoomFactor);
      const heightScale = maxViewportHeight / (baseHeight * zoomFactor);

      // If the zoomed content is still smaller than the viewport, use the zoom factor directly
      if (baseWidth * zoomFactor <= maxViewportWidth && baseHeight * zoomFactor <= maxViewportHeight) {
        return {
          width: baseWidth * zoomFactor,
          height: baseHeight * zoomFactor
        };
      }

      // Otherwise, apply additional scaling to fit in viewport
      const scale = Math.min(widthScale, heightScale, 1);

      return {
        width: baseWidth * zoomFactor * scale,
        height: baseHeight * zoomFactor * scale
      };
    }

    // For fullscreen mode, use zoom level directly
    return {
      width: baseWidth * zoomFactor,
      height: baseHeight * zoomFactor
    };
  };

  // Render the comparison view based on the selected mode
  const renderComparison = () => {
    const imageUrl = getFigmaImageUrl(frame);
    const optimalSize = calculateOptimalSize();

    // Set container dimensions based on optimal size
    const containerWidth = `${optimalSize.width}px`;
    const containerHeight = `${optimalSize.height}px`;

    if (comparisonMode === 'slider') {
      return (
        <div className="relative" style={{
          width: containerWidth,
          height: containerHeight,
          maxWidth: '100%',
          maxHeight: '100%'
        }}>
          <div className="absolute top-0 left-0 right-0 flex justify-center z-10 bg-black bg-opacity-50 text-white typography-caption py-1 rounded-t">
            Drag slider to compare
          </div>
          <ReactCompareSlider
            itemOne={
              <ReactCompareSliderImage
                src={imageUrl}
                alt="Original Figma design"
                style={{
                  objectFit: 'contain',
                  backgroundColor: '#f0f0f0'
                }}
              />
            }
            itemTwo={
              <div className="w-full h-full flex items-center justify-center bg-white">
                <CodeViewer
                  outputPreviewOnly={true}
                />
              </div>
            }
            position={50}
            style={{
              width: '100%',
              height: '100%',
            }}
          />
        </div>
      );
    } else {
      // Side-by-side comparison mode
      const isNarrowViewport = window.innerWidth < 768;

      return (
        <div className="flex gap-4 max-w-full max-h-full justify-center"
          style={{
            width: '100%',
            flexDirection: isNarrowViewport ? 'column' : 'row',
            flexWrap: 'nowrap'
          }}>
          {/* Original Figma design */}
          <div
            className="bg-white shadow-lg rounded overflow-hidden"
            style={{
              width: isNarrowViewport ? '100%' : '48%',
              height: isNarrowViewport ? `${optimalSize.height / 2}px` : containerHeight,
              maxHeight: isNarrowViewport ? `50vh` : '100%'
            }}
          >
            <div className="bg-semantic-gray-200 p-1 typography-caption font-weight-medium text-center border-b border-semantic-gray-300">
              Original Figma Design
            </div>
            <div className="flex items-center justify-center bg-semantic-gray-50" style={{ height: 'calc(100% - 24px)' }}>
              <img
                src={imageUrl}
                alt={`${frame.name} - Original`}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  objectFit: 'contain'
                }}
              />
            </div>
          </div>

          {/* Generated HTML */}
          <div
            className="bg-white shadow-lg rounded overflow-hidden"
            style={{
              width: isNarrowViewport ? '100%' : '48%',
              height: isNarrowViewport ? `${optimalSize.height / 2}px` : containerHeight,
              maxHeight: isNarrowViewport ? `50vh` : '100%'
            }}
          >
            <div className="bg-semantic-gray-200 p-1 typography-caption font-weight-medium text-center border-b border-semantic-gray-300">
              Generated HTML
            </div>
            <div style={{ height: 'calc(100% - 24px)' }}>
              <CodeViewer
               outputPreviewOnly={true}
              />
            </div>
          </div>
        </div>
      );
    }
  };

  // Render the HTML output view
  const renderHtmlOutput = () => {
    const optimalSize = calculateOptimalSize();

    return (
      <div
        className="bg-white shadow-lg rounded overflow-hidden"
        style={{
          width: `${optimalSize.width}px`,
          height: `${optimalSize.height}px`,
          maxWidth: '100%',
          maxHeight: '100%'
        }}
      >
        <CodeViewer
          outputPreviewOnly={true}
        />
      </div>
    );
  };

  // Render the design view
  const renderDesignView = () => {
    const optimalSize = calculateOptimalSize();

    return (
      <div
        className="bg-white shadow-lg rounded overflow-hidden"
        style={{
          width: `${optimalSize.width}px`,
          height: `${optimalSize.height}px`,
          maxWidth: '100%',
          maxHeight: '100%'
        }}
      >
        <img
          src={getFigmaImageUrl(frame)}
          alt={frame.name}
          className="w-full h-full object-contain"
        />
      </div>
    );
  };

  // Add a zoom indicator for all modes
  const renderZoomControls = () => {
    return (
      <div className="flex items-center bg-semantic-gray-100 rounded-md border border-semantic-gray-300 mr-2">
        <button
          onClick={handleZoomOut}
          className="p-2 typography-caption flex items-center gap-1 bg-white text-semantic-gray-600 hover:bg-semantic-gray-50 disabled:opacity-50"
          disabled={zoomLevel <= 50}
          title="Zoom out"
        >
          <ZoomOut className="h-4 w-4" />
        </button>
        <span className="px-2 typography-caption font-weight-medium">{zoomLevel}%</span>
        <button
          onClick={handleZoomIn}
          className="p-2 typography-caption flex items-center gap-1 bg-white text-semantic-gray-600 hover:bg-semantic-gray-50 disabled:opacity-50"
          disabled={zoomLevel >= 200}
          title="Zoom in"
        >
          <ZoomIn className="h-4 w-4" />
        </button>
        <button
          onClick={handleResetZoom}
          className="p-2 typography-caption flex items-center gap-1 bg-white text-semantic-gray-600 hover:bg-semantic-gray-50"
          title="Reset zoom"
        >
          <Move className="h-4 w-4" />
        </button>
      </div>
    );
  };

  // Fullscreen mode
  if (isFullScreen) {
    return (
      <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex flex-col">
        <div className="flex justify-between items-center p-4 bg-black bg-opacity-50">
          <div className="text-white">
            <h3 className="font-weight-medium">{frame.name}</h3>
            <div className="typography-body-sm text-semantic-gray-300">
              Zoom: {zoomLevel}% | {Math.round(frame.dimensions.width * zoomLevel / 100)} × {Math.round(frame.dimensions.height * zoomLevel / 100)}
            </div>
          </div>
          <div className="flex gap-2">
            {/* View switcher in fullscreen mode */}
            <div className="flex items-center bg-semantic-gray-800 rounded-md overflow-hidden mr-4">
              <button
                className={`px-3 py-1 typography-caption ${viewMode === 'design' ? 'bg-primary-600 text-white' : 'text-semantic-gray-300 hover:bg-semantic-gray-700'}`}
                onClick={() => setViewModeAndGenerateIfNeeded('design')}
              >
                Design
              </button>
              <button
                className={`px-3 py-1 typography-caption ${viewMode === 'code' ? 'bg-primary-600 text-white' : 'text-semantic-gray-300 hover:bg-semantic-gray-700'}`}
                onClick={() => setViewModeAndGenerateIfNeeded('code')}
              >
               Output
              </button>
              <button
                className={`px-3 py-1 typography-caption ${viewMode === 'compare' ? 'bg-primary-600 text-white' : 'text-semantic-gray-300 hover:bg-semantic-gray-700'}`}
                onClick={() => setViewModeAndGenerateIfNeeded('compare')}
              >
                Compare
              </button>
            </div>

            {viewMode === 'compare' && (
              <DynamicButton
                type="button"
                size="small"
                variant="secondary"
                icon={comparisonMode === 'slider' ? SplitSquareVertical : SlidersHorizontal}
                onClick={toggleComparisonMode}
                tooltip={comparisonMode === 'slider' ? "Switch to side-by-side view" : "Switch to slider view"}
              />
            )}
            <DynamicButton
              type="button"
              size="small"
              variant="secondary"
              icon={ZoomIn}
              onClick={handleZoomIn}
              tooltip="Zoom in"
              disabled={zoomLevel >= 200}
            />
            <DynamicButton
              type="button"
              size="small"
              variant="secondary"
              icon={ZoomOut}
              onClick={handleZoomOut}
              tooltip="Zoom out"
              disabled={zoomLevel <= 50}
            />
            <DynamicButton
              type="button"
              size="small"
              variant="secondary"
              icon={Move}
              onClick={handleResetZoom}
              tooltip="Reset zoom"
            />
            <DynamicButton
              type="button"
              size="small"
              variant="secondary"
              icon={Minimize2}
              onClick={toggleFullScreen}
              tooltip="Exit fullscreen"
            />
          </div>
        </div>

        <div className="flex-1 overflow-auto flex items-center justify-center p-8">
          {viewMode === 'compare' && renderComparison()}
          {viewMode === 'design' && renderDesignView()}
          {viewMode === 'code' && renderHtmlOutput()}
        </div>
      </div>
    );
  }

  // Normal (non-fullscreen) mode
  return (
    <div className="flex flex-col h-full" ref={containerRef}>
      <div className="p-4 border-b border-semantic-gray-200 flex justify-between items-center">
        <div>
          <h3 className="font-weight-medium text-semantic-gray-900">{frame.name}</h3>
          <div className="typography-body-sm text-semantic-gray-500">
            {Math.round(frame.dimensions.width * zoomLevel / 100)} × {Math.round(frame.dimensions.height * zoomLevel / 100)} ({zoomLevel}%)
          </div>
        </div>
        <div className="flex gap-2 items-center">
          {/* View mode selector */}
          <div className="flex items-center rounded-md border border-semantic-gray-300 mr-2">
            <button
              onClick={() => setViewModeAndGenerateIfNeeded('design')}
              className={`p-2 typography-caption flex items-center gap-1 ${viewMode === 'design'
                  ? 'bg-primary-50 text-primary'
                  : 'bg-white text-semantic-gray-600 hover:bg-semantic-gray-50'
                }`}
              title="View original design"
            >
              <Eye className="h-4 w-4" />
              <span className="hidden sm:inline">Design</span>
            </button>
            <button
              onClick={() => setViewModeAndGenerateIfNeeded('code')}
              className={`p-2 typography-caption flex items-center gap-1 ${viewMode === 'code'
                  ? 'bg-primary-50 text-primary'
                  : 'bg-white text-semantic-gray-600 hover:bg-semantic-gray-50'
                }`}
              title="View generated output"
            >
              <Code className="h-4 w-4" />
              <span className="hidden sm:inline">Output</span>
            </button>
            <button
              onClick={() => setViewModeAndGenerateIfNeeded('compare')}
              className={`p-2 typography-caption flex items-center gap-1 ${viewMode === 'compare'
                  ? 'bg-primary-50 text-primary'
                  : 'bg-white text-semantic-gray-600 hover:bg-semantic-gray-50'
                }`}
              title="Compare design and code"
            >
              <SplitSquareVertical className="h-4 w-4" />
              <span className="hidden sm:inline">Compare</span>
            </button>
          </div>

          {/* Add zoom controls to non-fullscreen mode */}
          {renderZoomControls()}

          {/* Comparison mode selector (only visible when in compare mode) */}
          {viewMode === 'compare' && (
            <div className="flex items-center rounded-md border border-semantic-gray-300 mr-2">
              <button
                onClick={() => setComparisonMode('slider')}
                className={`p-2 typography-caption ${comparisonMode === 'slider'
                    ? 'bg-primary-50 text-primary'
                    : 'bg-white text-semantic-gray-600 hover:bg-semantic-gray-50'
                  }`}
                title="Slider view"
              >
                <SlidersHorizontal className="h-4 w-4" />
              </button>
              <button
                onClick={() => setComparisonMode('sideBySide')}
                className={`p-2 typography-caption ${comparisonMode === 'sideBySide'
                    ? 'bg-primary-50 text-primary'
                    : 'bg-white text-semantic-gray-600 hover:bg-semantic-gray-50'
                  }`}
                title="Side-by-side view"
              >
                <SplitSquareVertical className="h-4 w-4" />
              </button>
            </div>
          )}

          <DynamicButton
            type="button"
            size="small"
            variant="primary"
            icon={Maximize2}
            tooltip="Fullscreen"
            onClick={toggleFullScreen}
          />
        </div>
      </div>

      <div className="flex-1 overflow-auto p-6 flex items-center justify-center bg-semantic-gray-100">
        {viewMode === 'compare' && renderComparison()}
        {viewMode === 'design' && renderDesignView()}
        {viewMode === 'code' && renderHtmlOutput()}
      </div>
    </div>
  );
};

export default PrototypeTab;