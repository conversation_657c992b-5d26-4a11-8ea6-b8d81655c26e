// EmptyStateDesign.jsx
'use client'
import Image from 'next/image';
import figmaEmptyImg from "../../../public/images/figma_empty.svg";
import { Plus } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";

export default function EmptyStateDesign({ type = 'figma', onAddClick }) {
  const getContent = () => {
    if (type === 'figma') {
      return {
        title: "This is where you'll manage your Figma Designs",
        description: "Import and preview your Figma designs",
        buttonText: "Add Figma Design"
      };
    } else {
      return {
        title: "This is where you'll manage your Images",
        description: "Upload and organize your design images",
        buttonText: "Upload Images"
      };
    }
  };

  const content = getContent();

  return (
    <div className='flex items-center justify-center h-full min-h-[65vh] bg-gradient-to-b from-transparent to-semantic-gray-50 pt-0 pb-16'>
      <div className="w-full max-w-md text-center flex flex-col items-center bg-white p-7 rounded-xl shadow-sm transition-all duration-300 hover:shadow-md border border-semantic-gray-100 -mt-12">
        <div className="mb-6 relative transition-transform duration-500 hover:scale-105">
          <Image
            src={figmaEmptyImg}
            alt="Designer with artboards"
            width={220}
            height={220}
            className="mx-auto"
          />
        </div>

        <h2 className="typography-heading-2 font-weight-semibold text-semantic-gray-800 mb-3 px-4 tracking-tight">
          {content.title}
        </h2>
        <p className="typography-body text-semantic-gray-600 mb-6 px-4 leading-relaxed">
          {content.description}
        </p>

        {onAddClick && (
          <DynamicButton
            type="button"
            size="default"
            variant="primary"
            icon={Plus}
            onClick={onAddClick}
            text={content.buttonText}
          />
        )}
      </div>
    </div>
  );
}