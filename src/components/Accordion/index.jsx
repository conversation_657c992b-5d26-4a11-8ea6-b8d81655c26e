import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

const AccordionI = AccordionPrimitive.Root;

const Accordion = ({ children ,items}) => {
  
  const [openAccordionIndexes, setOpenAccordionIndexes] = React.useState([]);

  React.useEffect(() => {
    const count = items.length;
    
    if (count > 0) {
      const lastIndex = count - 1;
      
      setOpenAccordionIndexes([lastIndex.toString()]); // Ensure the last index is opened
    }
  }, [children]);
  

  return (
    <AccordionI
      type="multiple"
      value={openAccordionIndexes}
      onValueChange={setOpenAccordionIndexes}
      className="w-full border-b last:border-b-0"
    >
      {children}
    </AccordionI>
  );
};

// The rest of your Accordion components remain the same
const AccordionItem = React.forwardRef(({ className, value, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    value={value} // Value added here to reference each item
    className={cn(
      "border-b border-semantic-gray-200 ",
      className
    )}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        ref={ref}
        className={cn(
          "flex w-full items-center justify-between py-2 px-4 typography-body-sm font-weight-semibold transition-all",
          className
        )}
        {...props}
      >
        {children}
        <span className="ml-2">
          <ChevronDown className="h-4 w-4 transition-transform duration-300 ease-in-out data-[state=open]:rotate-180" />
        </span>
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  )
);
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Content
      ref={ref}
      className={cn(
        "overflow-y-auto custom-scrollbar typography-body-sm transition-all text-semantic-gray-700 data-[state=closed]:max-h-0 data-[state=open]:max-h-[500px]",
        className
      )}
      {...props}
    >
      <div className="py-2 px-4">{children}</div>
    </AccordionPrimitive.Content>
  )
);
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { AccordionI, Accordion, AccordionItem, AccordionTrigger, AccordionContent };
