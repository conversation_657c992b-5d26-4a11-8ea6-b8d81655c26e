import React, { useState, useRef } from 'react';
import { FileUp } from 'lucide-react';
import ImportProjectModal from '../ProjectsList/ImportProjectModal';

const ImportProjectForm = ({ createProject, isLight }) => {
  const [formData, setFormData] = useState({
    projectName: '',
    projectPurpose: 'To Understand the Code Base',
    files: []
  });

  const [isImportModalOpen, setIsImportModalOpen] = useState(false);

  const fileInputRef = useRef(null);

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;
    
    let fieldName = name;

    if (fieldName == "projectTitle") {
      fieldName = "projectName";
    }

    if (type === 'file' && files) {
      setFormData(prevState => ({
        ...prevState,
        files: Array.from(files)
      }));
    } else {
      setFormData(prevState => ({
        ...prevState,
        [fieldName]: value
      }));
    }
  };

  const handleFileUpload = () => {
    
  };

  const handleImportProject = () => {
    setIsImportModalOpen(true);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const requirement = `${formData.projectPurpose} for project: ${formData.projectName}`;
    createProject(requirement);
  };

  return (
    <div className={`flex flex-col bg-none ${isLight ? "text-gray-800" : "text-gray-200"} mt-8`}>
      <form onSubmit={handleSubmit} className="flex flex-col flex-1">
        <div className="flex-1">
          {/* Project Name Field */}
          <div className="mb-6">
            <label className={`block mb-2 typography-body-sm font-weight-light ${isLight ? "text-gray-700" : "text-gray-300"}`}>
              Project Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="projectName"
              value={formData.projectName}
              onChange={handleChange}
              placeholder="e.g. Movie App"
              className={`w-full p-3 ${isLight ? "bg-white border-gray-200 text-gray-800 placeholder-gray-400" : "bg-white/5 border-zinc-700 text-gray-300 placeholder-gray-500"} border rounded typography-body-sm focus:outline-none focus:ring-1 focus:ring-orange-500`}
              required
            />
          </div>

        </div>

        {/* Import Buttons at Bottom */}
        <div className="grid grid-cols-1 gap-3 mt-4 absolute bottom-4 left-4 right-4">
          <button
            type="button"
            onClick={handleImportProject}
            className={`w-full flex items-center justify-center py-3 px-4 border ${isLight ? "border-gray-200 text-gray-700 hover:bg-gray-50" : "border-zinc-700 text-white hover:bg-white/5"} rounded typography-body-sm font-weight-medium transition-colors`}
          >
            <FileUp className={`w-4 h-4 mr-2 ${isLight ? "text-gray-600" : "text-gray-400"}`} />
            Import Assets
          </button>

             {/* <button
                type="button"
                className={`flex items-center justify-center py-3 px-4 border ${isLight ? "border-gray-200 text-gray-700 hover:bg-gray-50" : "border-zinc-700 text-white hover:bg-white/5"} rounded typography-body-sm font-weight-medium transition-colors`}
              >
                <Image
                  src={figmaLogo}
                  alt="Figma Logo"
                  width={16}
                  height={16}
                  className={`mr-2 ${isLight ? "opacity-80" : "opacity-60"}`}
                />
                Import Figma
              </button> */}
        </div>

      </form>

      <ImportProjectModal
        isOpen={isImportModalOpen}
        onClose={() => setIsImportModalOpen(false)}
        initialProjectName={formData.projectName}
        handleNameChange={handleChange}
      />
    </div>
  );
};

export default ImportProjectForm;