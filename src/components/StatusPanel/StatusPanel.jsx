"use client";

import React, { useEffect, useContext, useState, useCallback } from "react";
import { createPortal } from "react-dom";
import {
  FaRegTrashAlt,
  FaEllipsisH,
  FaCompress,
  FaTimes,
  FaSync,
  FaChevronLeft,
  FaChevronRight,
  FaArrowLeft,
  FaClipboardCheck,
  FaEye
} from "react-icons/fa";
import { FaListCheck } from "react-icons/fa6";
import "@/styles/components/StatusPanel.css";

import { ExecutionContext } from "../Context/ExecutionContext";
import { usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";
import ChatPanel from "../CodeGen/ChatPanel";
import { ModalContext } from "../Context/ModalContext";
import { StateContext } from "../Context/StateContext";
import ConnectionIndicator from "../ConnectionIndicator/ConnectionIndicator";
import DeleteProjectModal from "../Modal/DeleteProjectModal";
import { FaStop } from "react-icons/fa6";
import { BootstrapTooltip } from "../UIComponents/ToolTip/Tooltip-material-ui";
import TaskDisplay from "./TaskDisplay";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "../UIComponents/Badge/Badge";
import ReconfigModal from "../Modal/ReconfigModal";
import { approveReconfiguration } from "@/utils/api";
import { getPastTasks, getTotalPastTasks } from "@/utils/configureAPI";
import { getUserAvatarV2 } from "@/utils/avatarUtils";
import { formatDateTime } from "@/utils/datetime";
import { Clock } from "lucide-react";

const StatusPanel = () => {
  const {
    currentTaskId,
    setCurrentTaskId,
    setConfigStatus,
    configStatus,
    isConnected,
    currentTaskDetailsId,
    pauseTask,
    confirmAndDelete,
    cancelDelete,
    isDeleting,
    isDeleteModalOpen,
    setIsDeleteModalOpen,

    lastTime,
    configLabel,
    setTaskStatusUpdate,
    reconfigApprovedOrRejected,
    setReconfigApprovedOrRejected,
    autoNavigateEnabled,
    setAutoNavigateEnabled,
  } = useContext(ExecutionContext);

  // Get the context for state management
  useContext(StateContext);

  const { setIsModalOpen: setGlobalModalOpen } = useContext(ModalContext);

  const [configurationStatus, setConfigurationStatus] = useState({});
  const [progress, setProgress] = useState(0);
  const [etaInfo, setEtaInfo] = useState({});
  const [taskStatus, setTaskStatus] = useState("Idle");
  const [title, setTitle] = useState("");
  const { showAlert } = useContext(AlertContext);
  const pathname = usePathname();
  const id = pathname.split("/")[3];
  const pathParts = pathname.split("/");
  const projectId = pathParts[3];
  const [isCodeModalOpen, setIsCodeModalOpen] = useState(false);
  const [, setTaskDetails] = useState(null);
  const [nodeKeys, setNodeKeys] = useState([]);
  const [isStopModalOpen, setIsStopModalOpen] = useState(false);
  const [formattedResponse, setFormattedResponse] = useState(false);
  const [reconfigModal, setReconfigModal] = useState(false);
  const [pastTasks, setPastTasks] = useState([]);
  const [pastTasksLoading, setPastTasksLoading] = useState(false);
  const [pastTasksPage, setPastTasksPage] = useState(1);
  const [totalPastTasks, setTotalPastTasks] = useState(0);
  const [tasksPerPage] = useState(5);
  const [showPastTasks, setShowPastTasks] = useState(false);
  const [pastSelectedTask, setPastSelectedTask] = useState(null);
  // Reset component state when projectId changes
  useEffect(() => {
    // Clear past tasks data when project changes
    setPastTasks([]);
    setTotalPastTasks(0);
    setPastTasksPage(1);
    setPastSelectedTask(null);
    setShowPastTasks(false);

    // Reset local state that might contain data from previous project
    setProgress(0);
    setEtaInfo({})
    setTaskStatus("Idle");
    setTitle("");
    setNodeKeys([]);
    setFormattedResponse(false);
    setConfigurationStatus({});
  }, [projectId]);

  // Update the modal context whenever a modal opens or closes
  useEffect(() => {
    setGlobalModalOpen(isStopModalOpen || isDeleteModalOpen || reconfigModal);
  }, [isStopModalOpen, isDeleteModalOpen, reconfigModal, setGlobalModalOpen]);

  const handleStopTask = () => {
    setIsStopModalOpen(true);
  };

  const confirmAndStopTask = async () => {
    pauseTask();
    setTaskStatus("Cancelled");
    setTaskStatusUpdate("Cancelled")
    setIsStopModalOpen(false);
  };

  const getShownAlerts = () => {
    return JSON.parse(localStorage.getItem("shownAlerts")) || [];
  };

  const setShownAlerts = (taskId) => {
    const shownAlerts = getShownAlerts();
    if (!shownAlerts.includes(taskId)) {
      shownAlerts.push(taskId);
      localStorage.setItem("shownAlerts", JSON.stringify(shownAlerts));
    }
  };

  const getFailureMessage = () => {
    const formattedLabel = configLabel
      .replace(/-/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
    return `The ${formattedLabel} process encountered an issue. Please check the logs for details and try again.`;

  };
  const getSuccessMessage = () => {
    const formattedLabel = configLabel
      .replace(/-/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
    return `The ${formattedLabel} process has been successfully completed. You can proceed with the next steps.`;
  };

  useEffect(() => {
    if (configStatus[currentTaskId]) {
      console.log('StatusPanel - configStatus data:', configStatus[currentTaskId]);
      console.log('StatusPanel - eta_info:', configStatus[currentTaskId].eta_info);
      setProgress(configStatus[currentTaskId].progress || 0);
      setEtaInfo(configStatus[currentTaskId].eta_info || {});
      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setTaskStatusUpdate(configStatus[currentTaskId].task_status || "Idle")
      setConfigurationStatus(configStatus[currentTaskId].configuration_status || {});
      setFormattedResponse(configStatus[currentTaskId].formatted_response || {});
      setTaskDetails(null);
    }
    const shownAlerts = getShownAlerts();

    if (taskStatus.toLowerCase() === "failed" && !shownAlerts.includes(currentTaskId)) {
      showAlert(getFailureMessage(), "error");
      setShownAlerts(currentTaskId);
    } else if (taskStatus.toLowerCase() === "complete" && !shownAlerts.includes(currentTaskId)) {
      showAlert(getSuccessMessage(), "success");
      setShownAlerts(currentTaskId);
    }
  }, [currentTaskId, configStatus[currentTaskId]]);

  useEffect(() => {
    setTaskDetails(currentTaskDetailsId);
  }, [currentTaskDetailsId]);

  useEffect(() => {
    const keys = Object.keys(formattedResponse);
    if (keys.length > 0) {
      setNodeKeys(keys);
      setTitle(taskStatus.toLowerCase() === 'pending' ? "" : configStatus[currentTaskId]?.title || "");
    }
  }, [configurationStatus]);

  useEffect(() => {
    let intervalId;
    // Only start auto-refresh if there's an active task
    if (currentTaskId && taskStatus.toLowerCase() === 'in_progress') {
        intervalId = setInterval(() => {
            setCurrentTaskId(null);
            setTimeout(() => {
                setCurrentTaskId(currentTaskId);
            }, 500);
        }, 300000); // Refresh every 10 seconds
    }
    // Cleanup function to clear interval
    return () => {
        if (intervalId) {
            clearInterval(intervalId);
        }
    };
}, [currentTaskId, taskStatus]);

const handleRefresh = async (callback) => {
  setCurrentTaskId(null);
  setTimeout(() => {
      setCurrentTaskId(currentTaskId);
      if (callback && typeof callback === 'function') {
        callback();
      }
  }, 2000);
};
  const proceedWithDelete = async () => {
    await pauseTask(true);
    setConfigStatus({ ...configStatus, currentTaskId: {} });
    setCurrentTaskId(null);
    setProgress(0);
    setEtaInfo({});
    setTaskStatus("Idle");
    setTaskStatusUpdate("Idle")
    setConfigurationStatus({});
    setNodeKeys([]);
    setTitle("");
    setIsDeleteModalOpen(false);
  };

  const CodeBlockContent = ({ children }) => (
    <div className="relative custom-scrollbar overflow-y-auto h-40 bg-[#1c1c1c] text-[#ffac60] p-4 rounded-md">
      <pre className="whitespace-pre-wrap ml-5 -mt-1.5">{children}</pre>
      <div className="absolute left-0 top-0 bottom-0 w-8 bg-[#1c1c1c] text-semantic-gray-500 text-right p-2">
        {Array.from({ length: 100 }, (_, i) => (
          <div key={i}>{i + 1}</div>
        ))}
      </div>
    </div>
  );

  const handleApprove = async (projectId) => {
    const response = await approveReconfiguration(projectId, currentTaskId);

    if (response.message === "Reconfiguration approved successfully") {
      showAlert("Reconfiguration approved successfully", "success");
      setReconfigApprovedOrRejected(true);
      setReconfigModal(false);
    }
  }

  const CodeBlock = () => {
    const [isHovered, setIsHovered] = useState(false);

    const ButtonWithTooltip = ({ text, onClick, tooltip }) => (
      <div
        className="relative flex items-center"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <button
          className="flex items-center space-x-2 bg-[#1f6feb] text-white font-weight-bold p-2 rounded-lg"
          onClick={onClick}
        >
          <span>{text}</span>
        </button>
        {isHovered && tooltip && (
          <div className="absolute left-full right-10 top-1/2 w-96 transform -translate-y-1/2 ml-2 p-2 bg-semantic-gray-800 text-white typography-body-sm rounded shadow-lg z-10">
            {tooltip}
            <div className="absolute left-[-8px] top-1/2 transform -translate-y-1/2 w-0 h-0 border-t-8 border-t-transparent border-b-8 border-b-transparent border-r-8 border-r-gray-800"></div>
          </div>
        )}
      </div>
    );

    const ButtonBar = () => {
      const handleDeleteClick = () => {
        // Add delete functionality
      };

      return (
        <div className="flex justify-start space-x-4 p-4 bg-white text-black">
          <button className="flex items-center space-x-2 text-black font-weight-bold px-3 bg-white border shadow-md rounded-lg">
            <FaEllipsisH />
          </button>
          <button className="flex items-center space-x-2 text-black font-weight-bold px-4 bg-white border shadow-md rounded-lg">
            Edit
          </button>
          <ButtonWithTooltip
            text="Regenerate"
            onClick={handleDeleteClick}
            tooltip="This will update and generate the subsequent steps based on the latest changes"
          />
        </div>
      );
    };

    return (
      <div className="transition-all duration-300">
        <CodeBlockContent>
          {`function example() {

}`}
        </CodeBlockContent>
        <div className="border mt-4">
          <div className="flex justify-between p-4 bg-white text-black">
            <div className="text-md">
              Environment Setup - Create a Python virtual environment
            </div>
            <div className="text-md">8 Sep, 2024 10:12 PM</div>
          </div>
          <ButtonBar />
        </div>
      </div>
    );
  };

  const togglePastTasksView = () => {
    setShowPastTasks(!showPastTasks);
  };

  const fetchPastTasks = useCallback(async () => {
    if (!projectId) return;

    setPastTasksLoading(true);
    // Clear previous data before fetching new data
    setPastTasks([]);
    setTotalPastTasks(0);

    try {
      // Fetch both data in parallel for better performance
      const [tasksData, totalData] = await Promise.all([
        getPastTasks(projectId, pastTasksPage, tasksPerPage),
        getTotalPastTasks(projectId)
      ]);

      // Add a small delay to ensure UI updates properly
      setTimeout(() => {
        setPastTasks(tasksData);
        setTotalPastTasks(totalData.total);
        setPastTasksLoading(false);
      }, 300);
    } catch (error) {

      // Show empty state on error
      setPastTasks({ tasks: [] });
      setPastTasksLoading(false);
    }
  }, [projectId, pastTasksPage, tasksPerPage]);

  useEffect(() => {
    // Fetch past tasks when the component mounts or when showPastTasks becomes true
    if (currentTaskId === null || showPastTasks) {
      fetchPastTasks();
    }
  }, [currentTaskId, fetchPastTasks, showPastTasks]);

  // Ensure we fetch data when the page changes
  useEffect(() => {
    if (showPastTasks) {
      fetchPastTasks();
    }
  }, [pastTasksPage, fetchPastTasks, showPastTasks]);

  const formatTaskType = (type) => {
    if (!type) return "Auto Configure";

    switch (type.toLowerCase()) {
      case "auto-extract": return "Auto Extraction";
      case "re-config": return "Re-Configure";
      default: return "Auto Configure";
    }
  };

  const PastTasksList = () => {
    if (pastTasksLoading) {
      return (
        <div className="flex justify-center items-center p-8">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-primary mb-3"></div>
            <p className="text-semantic-gray-600 typography-body-sm font-weight-medium">Loading configurations...</p>
          </div>
        </div>
      );
    }

    if (!pastTasks?.tasks || pastTasks.tasks.length === 0) {
      return (
        <div className="text-center p-8 bg-semantic-gray-50 rounded-md border border-semantic-gray-200">
          <FaListCheck className="text-semantic-gray-400 mx-auto mb-3" size={24} />
          <p className="text-semantic-gray-600 typography-body font-weight-medium">No past configurations found</p>
          <p className="text-semantic-gray-500 typography-body-sm font-weight-normal mt-1">Past auto-configurations will appear here</p>
        </div>
      );
    }

    return (
      <>
        <div className="overflow-x-auto rounded-md border border-semantic-gray-200 shadow-sm">
          <table className="min-w-full bg-custom-bg-primary">
            <thead className="bg-semantic-gray-50 text-semantic-gray-700 typography-body-sm">
              <tr>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Task ID</th>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Type</th>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Started By</th>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Status</th>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Date</th>
                <th className="py-3 px-4 text-left font-weight-medium border-b border-semantic-gray-200">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-semantic-gray-200">
              {pastTasks?.tasks?.map((task) => (
                <tr key={task.task_id} className="hover:bg-semantic-gray-50 transition-colors duration-150">
                  <td className="py-3 px-4 typography-body-sm font-weight-normal truncate max-w-[150px] text-semantic-gray-600">{task.task_id}</td>
                  <td className="py-3 px-4 typography-body-sm">
                    <Badge type={formatTaskType(task.type)} />
                  </td>
                  <td className="py-3 px-4 typography-body-sm">
                    <div className="flex items-center gap-2">
                      {getUserAvatarV2(task.user_details.Name, task.user_details.Picture)}
                      <span className="font-weight-medium text-semantic-gray-700">{task.user_details.Name}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 typography-body-sm">
                    {}
                    <span className={`px-2.5 py-1 rounded-md inline-flex items-center ${task.status === 'complete' || task?.task_status=== 'complete' ? 'bg-green-100 text-green-800' :
                      task.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                        task.status === 'in_progress' ? 'bg-primary-100 text-primary-800' :
                          'bg-semantic-gray-100 text-semantic-gray-800'
                      }`}>
                      <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${task.status === 'complete' || task?.task_status=== 'complete' ? 'bg-green-500' :
                        task.status === 'cancelled' ? 'bg-red-500' :
                          task.status === 'in_progress' ? 'bg-primary-500' :
                            'bg-semantic-gray-500'
                        }`}></span>
                      {task.status === 'complete' || task?.task_status=== 'complete'?"Complete":task.status === 'in_progress' ? 'In Progress' :
                        task.status.charAt(0).toUpperCase() + task.status.slice(1)}
                    </span>
                  </td>
                  <td className="py-3 px-4 typography-body-sm text-semantic-gray-600">{formatDateTime(task.start_time, true)}</td>
                  <td className="py-3 px-4">
                    <button
                      onClick={() => {
                        // Store the past task without affecting the current project's data
                        setPastSelectedTask(task);
                        setShowPastTasks(false);
                        // Don't call handleRefresh() here as it can reset the current task
                      }}
                      className="px-3 py-1.5 bg-white border border-semantic-gray-200 rounded-md hover:bg-semantic-gray-50 transition-colors duration-200 typography-body-sm font-weight-medium text-semantic-gray-700 shadow-sm flex items-center gap-1.5"
                    >
                      <FaEye className="text-semantic-gray-500" size={14} />
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {totalPastTasks > tasksPerPage && (
          <div className="flex justify-between items-center pt-4 pb-2 px-4">
            <div className="typography-body-sm text-semantic-gray-600">
              Showing {(pastTasksPage - 1) * tasksPerPage + 1}-
              {Math.min(pastTasksPage * tasksPerPage, totalPastTasks)} of {totalPastTasks}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPastTasksPage(prev => Math.max(1, prev - 1))}
                disabled={pastTasksPage === 1}
                className={`p-2 rounded-md border ${pastTasksPage === 1 ? 'text-semantic-gray-400 border-semantic-gray-200 bg-semantic-gray-50' : 'text-semantic-gray-700 border-semantic-gray-200 bg-white hover:bg-semantic-gray-50'}`}
              >
                <FaChevronLeft size={14} />
              </button>
              <button
                onClick={() => setPastTasksPage(prev =>
                  prev * tasksPerPage < totalPastTasks ? prev + 1 : prev
                )}
                disabled={pastTasksPage * tasksPerPage >= totalPastTasks}
                className={`p-2 rounded-md border ${pastTasksPage * tasksPerPage >= totalPastTasks
                  ? 'text-semantic-gray-400 border-semantic-gray-200 bg-semantic-gray-50'
                  : 'text-semantic-gray-700 border-semantic-gray-200 bg-white hover:bg-semantic-gray-50'
                  }`}
              >
                <FaChevronRight size={14} />
              </button>
            </div>
          </div>
        )}
      </>
    );
  };

  const menuOptions = [
    {
      label: "View Past Configurations",
      onClick: togglePastTasksView,
      icon: FaListCheck,
      tooltip: "View past auto-configurations"
    },
  ];

  if (showPastTasks) {
    return (
      <div className="bg-white rounded-md shadow-sm relative flex flex-col h-full">
        <div className="p-3 border-b flex justify-between items-center bg-semantic-gray-50">
          <div>
            <button
              onClick={togglePastTasksView}
              className="flex items-center p-2 hover:bg-semantic-gray-100 rounded-md transition-colors duration-200"
              aria-label="Go back"
            >
              <FaArrowLeft className="text-semantic-gray-600" />
            </button>
          </div>
          <div className="flex items-center">
            <FaListCheck className="text-primary mr-2" size={18} />
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">Past Auto-Configurations</h2>
          </div>
          <button
            onClick={fetchPastTasks}
            className="flex items-center gap-1.5 px-3 py-1.5 bg-white border border-semantic-gray-200 rounded-md hover:bg-semantic-gray-50 transition-colors duration-200 typography-body-sm font-weight-medium text-semantic-gray-700 shadow-sm"
            aria-label="Refresh past configurations"
          >
            <FaSync className="text-semantic-gray-600" size={14} /> Refresh
          </button>
        </div>
        <div className="p-4 flex-1 overflow-hidden min-h-0">
          <PastTasksList />
        </div>
      </div>
    );
  }

  if (pastSelectedTask) {
    return (
      <div className="bg-white rounded-md shadow-sm relative flex flex-col h-full ">
        <div className="p-3 border-b flex justify-between items-center bg-semantic-gray-50">
          <button
            onClick={() => {
              setPastSelectedTask(null);
              setShowPastTasks(true);
            }}
            className="flex items-center gap-1.5 px-3 py-1.5 bg-white border border-semantic-gray-200 rounded-md hover:bg-semantic-gray-50 transition-colors duration-200 typography-body-sm font-weight-medium text-semantic-gray-700 shadow-sm"
          >
            <FaArrowLeft className="text-semantic-gray-600" size={14} />
            Back to list
          </button>

          <div className="flex items-center">
            <FaClipboardCheck className="text-primary mr-2" size={18} />
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">Configuration Details</h2>
          </div>

          <div className="w-[100px]"></div> {/* Empty div for balance */}
        </div>

        <div className="p-4 flex-1 overflow-hidden min-h-0">
          <TaskDisplay
            taskId={pastSelectedTask.task_id}
            showStatusDisplay={true}
            showStatusDisplayProps={{
              "menuOptions": menuOptions,
              "handleRefresh": handleRefresh,
              "confirmAndDelete": confirmAndDelete,
              "handleStopTask": confirmAndStopTask,
            }}
          />
        </div>
        {isDeleteModalOpen && (
          <DeleteProjectModal
            isOpen={isDeleteModalOpen}
            onClose={cancelDelete}
            onDelete={proceedWithDelete}
            isDeleting={isDeleting}
            type="task"
          />
        )}
      </div>
    );
  }

  return (
    <div className="relative flex flex-col h-full w-full" id='statusPanel' style={{ height: '100%', width: '100%' }}>
      {/* Code Modal */}
      {isCodeModalOpen && (
        <div className="modal-container">
          <div className="relative bg-white w-11/12 h-5/6 rounded shadow-lg flex flex-col p-6 transition-all duration-300">
            <div className="absolute top-4 right-4 flex space-x-2">
              <button
                className="bg-semantic-gray-300 text-black font-weight-bold py-2 px-4 rounded"
                onClick={() => setIsCodeModalOpen(false)}
              >
                <FaCompress />
              </button>
              <button
                className="text-semantic-gray-600 hover:text-semantic-gray-800"
                onClick={() => setIsCodeModalOpen(false)}
              >
                <FaTimes size={20} />
              </button>
            </div>
            <div className="flex-grow flex">
              <div className="w-1/4 border-r">
                <ChatPanel />
              </div>
              <div className="w-full p-4">
                <CodeBlock />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Reconfig Modal */}
      {reconfigModal &&
        createPortal(<ReconfigModal onApprove={() => handleApprove(id)} onClose={() => setReconfigModal(false)} />, document.body)
      }

      {/* Main Content */}
      {nodeKeys.length > 0 || currentTaskId != null ? (
        <>
          <div className="compact-status-header">
            <div className="compact-header-content">
              {/* Left: Connection & Progress */}
              <div className="compact-left-section">
                <ConnectionIndicator isConnected={isConnected} />
                <div className="compact-progress-group">
                  <div className="compact-progress-bar">
                    <div
                      className={`compact-progress-fill ${progress >= 100 ? 'bg-green-500' : 'bg-primary'}`}
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <span className="compact-progress-text">{progress}%</span>
                </div>
                <span className={`compact-status-badge ${
                  taskStatus.toLowerCase() === 'complete' ? 'status-complete' :
                  taskStatus.toLowerCase() === 'in_progress' ? 'status-progress' : 'status-other'
                }`}>
                {taskStatus.toLowerCase() === 'in_progress' ? 'In Progress' :
                 taskStatus.toLowerCase() === 'complete' ? 'Complete' :
                 taskStatus.charAt(0).toUpperCase() + taskStatus.slice(1)}
                </span>
              </div>

              {/* Center: Time & Badge */}
              <div className="compact-center-section">
                <div className="compact-eta">
                  <Clock className="w-3 h-3" />
                  <span>Estimated Time</span>
                  <span className="compact-eta-value">
                    {etaInfo?.formatted_eta ||
                     (etaInfo?.remaining_minutes ? `${etaInfo.remaining_minutes} mins` : '0 mins')}
                  </span>
                </div>
                <div className="compact-action-badge">
                  {configLabel === "auto-extract" ? "Auto Extraction" :
                   configLabel === "re-config" ? "Re-Configure" : "Auto Configure"}
                </div>
              </div>

              {/* Right: Actions */}
              <div className="compact-right-section">
                {taskStatus.toLowerCase() !== 'idle' && (
                  <a
                    href={`https://us5.datadoghq.com/logs?query=task_id%3A${currentTaskId}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="compact-action-btn compact-view-log"
                  >
                    View Log
                  </a>
                )}

                <div className="compact-toggle">
                  <span className="compact-toggle-label">Auto Navigate</span>
                  <button
                    className={`compact-toggle-switch ${autoNavigateEnabled ? 'active' : ''}`}
                    onClick={() => setAutoNavigateEnabled(!autoNavigateEnabled)}
                  >
                    <span className="compact-toggle-knob" />
                  </button>
                </div>

                {configLabel === "re-config" && taskStatus === "complete" && !reconfigApprovedOrRejected && (
                  <button
                    className="compact-action-btn compact-review-btn"
                    onClick={() => setReconfigModal(true)}
                  >
                    Review
                  </button>
                )}

                <div className="compact-actions">
                  <BootstrapTooltip title="View Past Configurations">
                    <button className="compact-icon-btn" onClick={() => setShowPastTasks(true)}>
                      <FaListCheck size={14} />
                    </button>
                  </BootstrapTooltip>
                  <BootstrapTooltip title="Refresh">
                    <button className="compact-icon-btn" onClick={handleRefresh}>
                      <FaSync size={14} />
                    </button>
                  </BootstrapTooltip>
                  {taskStatus.toLowerCase() === 'in_progress' && (
                    <BootstrapTooltip title="Stop task">
                      <button className="compact-icon-btn compact-danger" onClick={handleStopTask}>
                        <FaStop size={14} />
                      </button>
                    </BootstrapTooltip>
                  )}
                  <BootstrapTooltip title="Delete task">
                    <button className="compact-icon-btn compact-danger" onClick={confirmAndDelete}>
                      <FaRegTrashAlt size={14} />
                    </button>
                  </BootstrapTooltip>
                </div>
              </div>
            </div>
          </div>

          {/* Task Display */}
          <div className="flex-1 overflow-hidden">
            <TaskDisplay taskId={currentTaskId} />
          </div>
        </>
      ) : (
        <div className="flex-1 flex items-center justify-center p-6">
          <EmptyStateView
            type="status"
            onClick={() => setShowPastTasks(true)}
            className="max-w-md mx-auto"
          />
        </div>
      )}

      {/* Stop Modal */}
      {isStopModalOpen && (
        <div className="fixed inset-0 bg-semantic-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-[100] flex items-center justify-center">
          <div className="p-4 border w-80 shadow-lg rounded-md bg-white">
            <div className="text-center">
              <h3 className="typography-body leading-6 font-weight-medium text-semantic-gray-900">Stop Execution</h3>
              <div className="px-4 py-2">
                <p className="typography-body-sm text-semantic-gray-500">
                  Are you sure you want to stop the execution?
                </p>
              </div>
              <div className="items-center px-4 py-2">
                <button
                  onClick={confirmAndStopTask}
                  className="px-3 py-1.5 bg-red-500 text-white typography-body-sm font-weight-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                >
                  Yes, Stop Execution
                </button>
                <button
                  onClick={() => setIsStopModalOpen(false)}
                  className="mt-2 px-3 py-1.5 bg-semantic-gray-100 text-semantic-gray-700 typography-body-sm font-weight-medium rounded-md w-full shadow-sm hover:bg-semantic-gray-200 focus:outline-none focus:ring-2 focus:ring-semantic-gray-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Modal */}
      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={cancelDelete}
          onDelete={proceedWithDelete}
          isDeleting={isDeleting}
          type="task"
        />
      )}
    </div>
  );
};

export default StatusPanel;
