import React from 'react';

const FeatureConfigurationSkeleton = () => {
  return (
    <div className="w-full max-h-[70vh] mx-1 animate-pulse">
      {/* Header Section */}
      <div className="mb-1 mx-1">
        <div className="h-8 bg-semantic-gray-200 rounded-md w-48 mb-4"></div>
        <div className="flex gap-8 mb-4">
          <div className="flex flex-col space-y-2">
            <div className="h-5 bg-semantic-gray-200 rounded w-40"></div>
            <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
          </div>
          <div className="flex flex-col space-y-2">
            <div className="h-5 bg-semantic-gray-200 rounded w-40"></div>
            <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      {/* Feature Sections */}
      <div className="flex-1 space-y-4">
        {/* User Management Section */}
        <div className="bg-white rounded-lg border border-semantic-gray-200 p-3 mx-1">
          <div className="flex items-center justify-between mb-1 bg-primary-50/50 rounded-md p-1.5">
            <div className="flex items-center gap-2">
              <div className="h-6 bg-semantic-gray-200 rounded w-32"></div>
              <div className="h-4 w-4 bg-semantic-gray-200 rounded-full"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-28"></div>
                <div className="h-9 bg-semantic-gray-200 rounded w-full"></div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-32"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Hub Section */}
        <div className="bg-white rounded-lg border border-semantic-gray-200 p-3 mx-1">
          <div className="flex items-center justify-between mb-1 bg-primary-50/50 rounded-md p-1.5">
            <div className="flex items-center gap-2">
              <div className="h-6 bg-semantic-gray-200 rounded w-28"></div>
              <div className="h-4 w-4 bg-semantic-gray-200 rounded-full"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-24"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-20"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-16"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Dashboard Section */}
        <div className="bg-white rounded-lg border border-semantic-gray-200 p-3 mx-1">
          <div className="flex items-center justify-between mb-1 bg-primary-50/50 rounded-md p-1.5">
            <div className="flex items-center gap-2">
              <div className="h-6 bg-semantic-gray-200 rounded w-36"></div>
              <div className="h-4 w-4 bg-semantic-gray-200 rounded-full"></div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-28"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex flex-col space-y-1">
                <div className="h-5 bg-semantic-gray-200 rounded w-32"></div>
                <div className="h-6 w-12 bg-semantic-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Buttons */}
      <div className="flex justify-between py-4 mt-2">
        <div className="h-10 bg-semantic-gray-200 rounded w-36"></div>
        <div className="flex gap-4">
          <div className="h-10 bg-semantic-gray-200 rounded w-28"></div>
          <div className="h-10 bg-semantic-gray-200 rounded w-40"></div>
        </div>
      </div>
    </div>
  );
};

export default FeatureConfigurationSkeleton;