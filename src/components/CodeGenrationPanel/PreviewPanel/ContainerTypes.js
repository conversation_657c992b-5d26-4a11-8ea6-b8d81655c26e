// Container status types and colors
export const ContainerStatusTypes = {
    RUNNING: 'running',
    STOPPED: 'stopped',
    STARTING: 'starting',
    BUILDING: 'building',
    ERROR: 'error',
    NOT_STARTED: 'not_started',
    UNKNOWN: 'unknown'
  };
  
  export const ContainerStatusConfig = {
    [ContainerStatusTypes.RUNNING]: {
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      icon: 'CheckCircle',
      label: 'Running'
    },
    [ContainerStatusTypes.STOPPED]: {
      color: 'text-red-500',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      icon: 'Square',
      label: 'Stopped'
    },
    [ContainerStatusTypes.STARTING]: {
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      icon: 'Loader2',
      label: 'Starting'
    },
    [ContainerStatusTypes.BUILDING]: {
      color: 'text-primary',
      bgColor: 'bg-primary-50',
      borderColor: 'border-primary-200',
      icon: 'Loader2',
      label: 'Building'
    },
    [ContainerStatusTypes.ERROR]: {
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-300',
      icon: 'AlertCircle',
      label: 'Error'
    },
    [ContainerStatusTypes.NOT_STARTED]: {
      color: 'text-semantic-gray-500',
      bgColor: 'bg-semantic-gray-50',
      borderColor: 'border-semantic-gray-200',
      icon: 'Clock',
      label: 'Not Started'
    },
    [ContainerStatusTypes.UNKNOWN]: {
      color: 'text-semantic-gray-400',
      bgColor: 'bg-semantic-gray-50',
      borderColor: 'border-semantic-gray-200',
      icon: 'HelpCircle',
      label: 'Unknown'
    }
  };
  
  export const WebSocketMessageTypes = {
    CONTAINERS: 'containers',
    CONTAINER_STATUSES: 'container_statuses',
    CONTAINER_STATUS: 'container_status',
    RUN_ALL_CONTAINERS_RESPONSE: 'run_all_containers_response',
    PREVIEW_ERROR: 'preview_error'
  };
  
  export const WebSocketCommands = {
    GET_ALL_CONTAINERS: 'get_all_containers',
    GET_ALL_STATUS: 'get_all_status',
    RUN_ALL_CONTAINERS: 'run_all_containers',
    RESTART_CONTAINER: 'restart_container'
  };