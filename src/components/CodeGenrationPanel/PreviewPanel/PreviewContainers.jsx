import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useState, useEffect } from "react";
import React from 'react';
import { Loader2, Circle, RefreshCw, ExternalLink, Copy, Check, Play } from "lucide-react";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { useSearchParams } from "next/navigation";

/**
 * Changes port from 3000 to 4000 in preview URLs
 * @param {string} url - The URL to process
 * @returns {string} - The URL with updated port
 */
const processPreviewUrl = (url) => {
  if (!url || typeof url !== 'string') return url;
  // Replace any occurrence of :3000 with :4000 in the URL
  return url.replace(':3000', ':4000');
};

function PreviewContainers() {
    const { 
        wsConnection, 
        previewContainersContext,
        setPreviewContainersContext,
        containers, 
        setContainers,
        selectedContainer, 
        setSelectedContainer
    } = useCodeGeneration();
    const searchParams = useSearchParams();
    const currentTaskId = searchParams.get("task_id");
    const [loading, setLoading] = useState(true);
    const [copied, setCopied] = useState(false);
    const [runningAll, setRunningAll] = useState(false);

    useEffect(() => {
        if (!wsConnection) return;
        if ((containers) && (containers.length > 0)){
            setLoading(false);
        }
    }, [wsConnection, selectedContainer, containers]);

    useEffect(() => {
        if (wsConnection?.readyState === WebSocket.OPEN) {
            wsConnection.send(JSON.stringify({
                type: "get_all_containers",
                task_id: currentTaskId,
                input_data: {}
            }));
        }
    }, [wsConnection, currentTaskId]);

    const refreshIframe = () => {
        // Find and refresh the iframe
        const iframe = document.querySelector('iframe[data-preview-container]');
        if (iframe) {
            iframe.src = iframe.src;
        }
    };

    const handleRefresh = () => {
        if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
            wsConnection.send(JSON.stringify({
                type: "restart_container",
                task_id: currentTaskId,
                input_data: {
                    container_name: selectedContainer
                }
            }));
            
            // Also refresh the iframe
            refreshIframe();
        }
    };

    const handleRunAllContainers = () => {
        if (wsConnection?.readyState === WebSocket.OPEN && currentTaskId) {
            setRunningAll(true);
            wsConnection.send(JSON.stringify({
                type: "run_all_containers",
                task_id: currentTaskId
            }));
            
            // Reset the running state after a delay
            setTimeout(() => setRunningAll(false), 2000);
        }
    };

    const handleCopy = async () => {
        const container = containers.find(c => c.name === selectedContainer);
        if (!container?.url) return;
        
        try {
            await navigator.clipboard.writeText(processPreviewUrl(container.url));
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (error) {
            console.error('Copy failed:', error);
        }
    };

    const getSelectedContainerUrl = () => {
        const container = containers.find(c => c.name === selectedContainer);
        return container?.url ? processPreviewUrl(container.url) : 'No preview URL';
    };

    const getSelectedContainer = () => {
        return containers.find(c => c.name === selectedContainer);
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'running': return 'text-success';
            case 'building': return 'text-warning';
            case 'stopped': return 'text-destructive';
            default: return 'text-semantic-gray-400';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center gap-2 px-3 py-1.5 text-semantic-gray-500 bg-custom-bg-primary border-b border-custom-border h-8">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span className="text-xs">Loading...</span>
            </div>
        );
    }

    if (!containers.length) {
        return (
            <div className="px-3 py-1.5 text-xs text-semantic-gray-400 bg-white border-b border-semantic-gray-200 h-8 flex items-center">
                No containers available
            </div>
        );
    }

    const selectedContainerData = getSelectedContainer();

    return (
        <div className="bg-white border-b border-semantic-gray-200">
            {/* Ultra Compact Single Row Header */}
            <div className="flex items-center justify-between px-3 py-1.5 h-10 bg-semantic-gray-50">
                <div className="flex items-center gap-3 min-w-0 flex-1">
                    {/* Run All Containers Button */}
                    
                    <BootstrapTooltip title="Run All Containers" placement="bottom">
                        <button
                            onClick={handleRunAllContainers}
                            disabled={runningAll}
                            className={`
                                flex items-center gap-1.5 px-2 py-1 rounded text-xs font-medium transition-all
                                ${runningAll 
                                    ? 'bg-primary-100 text-primary cursor-not-allowed' 
                                    : 'bg-primary-600 text-white hover:bg-primary-700 shadow-sm'
                                }
                                ${currentTaskId.startsWith("cm")? '': 'hidden'}
                            `}
                        >
                            {runningAll ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                            ) : (
                                <Play className="h-3 w-3" />
                            )}
                            <span>Run All</span>
                        </button>
                    </BootstrapTooltip>

                    {/* Container Tabs with Clear Tab Design */}
                    <div className="flex items-center bg-semantic-gray-200 rounded-md p-0.5">
                        {containers.map((container) => (
                            <button
                                key={container.name}
                                onClick={() => setSelectedContainer(container.name)}
                                className={`
                                    flex items-center gap-1.5 px-3 py-1 rounded text-xs font-medium transition-all relative
                                    ${selectedContainer === container.name 
                                        ? 'bg-white text-primary-600 shadow-sm border border-primary-200' 
                                        : 'text-semantic-gray-600 hover:text-semantic-gray-800 hover:bg-white/50'
                                    }
                                `}
                            >
                                <Circle 
                                    className={`h-2 w-2 fill-current ${
                                        selectedContainer === container.name ? 'text-primary' : getStatusColor(container.status)
                                    }`} 
                                />
                                <span className="capitalize whitespace-nowrap">{container.name}</span>
                                {selectedContainer === container.name && (
                                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"></div>
                                )}
                            </button>
                        ))}
                    </div>

                    {/* Status Info Only */}
                    {selectedContainerData && (
                        <div className="flex items-center gap-2 min-w-0">
                            <div className="h-3 w-px bg-semantic-gray-300"></div>
                            <div className={`inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs font-medium
                                ${selectedContainerData.status === 'running' ? 'bg-emerald-100 text-emerald-700' :
                                  selectedContainerData.status === 'building' ? 'bg-amber-100 text-amber-700' :
                                  'bg-semantic-gray-100 text-semantic-gray-600'}`}>
                                <Circle className={`h-1 w-1 fill-current ${getStatusColor(selectedContainerData.status)}`} />
                                {selectedContainerData.status}
                            </div>
                        </div>
                    )}
                </div>

                {/* Action Buttons - Always Show When Container Selected */}
                {selectedContainerData && (
                    <div className="flex items-center gap-0.5 ml-2">
                        {selectedContainerData.url && (
                            <>
                                <BootstrapTooltip title={copied ? "Copied!" : "Copy URL"} placement="bottom">
                                    <button
                                        onClick={handleCopy}
                                        className={`p-1 rounded transition-colors ${
                                            copied 
                                                ? "text-green-600 hover:bg-green-50" 
                                                : "text-semantic-gray-500 hover:text-semantic-gray-700 hover:bg-semantic-gray-100"
                                        }`}
                                    >
                                        {copied ? (
                                            <Check className="h-3 w-3" />
                                        ) : (
                                            <Copy className="h-3 w-3" />
                                        )}
                                    </button>
                                </BootstrapTooltip>
                                
                                <BootstrapTooltip title="Open in New Tab" placement="bottom">
                                    <a
                                        href={processPreviewUrl(selectedContainerData.url)}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="p-1 text-semantic-gray-500 hover:text-primary-600 hover:bg-primary-50 rounded transition-colors"
                                    >
                                        <ExternalLink className="h-3 w-3" />
                                    </a>
                                </BootstrapTooltip>
                            </>
                        )}
                        
                        <BootstrapTooltip title="Refresh Container" placement="bottom">
                            <button
                                onClick={handleRefresh}
                                className="p-1 text-semantic-gray-500 hover:text-semantic-gray-700 hover:bg-semantic-gray-100 rounded transition-colors"
                            >
                                <RefreshCw className="h-3 w-3" />
                            </button>
                        </BootstrapTooltip>
                    </div>
                )}
            </div>
        </div>
    );
}

export default PreviewContainers;