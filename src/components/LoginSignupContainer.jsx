"use client";

import React from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "./KaviaLongLogo";

const LoginSignupContainer = ({ children }) => {
  const router = useRouter();

  const handleLogoClick = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen py-10 flex flex-col items-center justify-center bg-black bg-[radial-gradient(circle,rgba(255,147,88,0.6)_0%,rgba(35,31,32,0.6)_60%)] relative z-0">
      <div className="fixed top-0 left-0 w-full h-full z-0 overflow-hidden backdrop-blur-[150px]">
      </div>

      <header
        className="fixed top-10 left-8 text-white typography-heading-2 font-weight-semibold z-20 cursor-pointer"
        onClick={handleLogoClick}
      >
        <KaviaLongLogo />
      </header>

      <div className="w-full flex flex-col items-center justify-center relative">
        {children}
      </div>

      <div className="flex mt-6 gap-3 typography-body-sm z-10">
        <div className="flex items-center justify-center text-semantic-gray-300 text-md space-x-4 z-50">
          <span>
            © 2025 Kavia AI
          </span>
          <span>|</span>
          <Link href="#">
            Support
          </Link>
          <span>|</span>
          <Link href="#">
            Privacy
          </Link>
          <span>|</span>
          <Link href="#">
            Terms
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LoginSignupContainer;