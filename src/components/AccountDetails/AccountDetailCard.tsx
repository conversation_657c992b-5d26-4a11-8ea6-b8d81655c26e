import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaEllipsisH, FaExclamation } from "react-icons/fa";
import { Menu } from '@headlessui/react';

interface AccountDetailCardProps {
    title: string;
    id: string;
    lastSynced: string;
    isConnected: boolean;
    connectedSettingsArray?: any[];
    handleMenuClick?(item?: any): any;
}

const AccountDetailCard: React.FC<AccountDetailCardProps> = ({
    title,
    id,
    lastSynced,
    isConnected,
    connectedSettingsArray,
    handleMenuClick
}) => {
    const _handleMenuClick = (item?: any) => {
        handleMenuClick && handleMenuClick(item)
    }
    return (
        <div className="flex items-center justify-between w-full bg-gradient-to-b from-semantic-gray-200 to-white shadow-md rounded-lg p-4 hover:shadow-lg transform transition duration-200">
            {/* <div className="flex items-center justify-between w-full bg-white shadow-md rounded-lg p-4 hover:shadow-lg transform transition duration-200"> */}
            <div className="flex items-center">
                <div className="w-12 h-12 flex-shrink-0">
                    <img
                        src="https://cdn-icons-png.flaticon.com/512/25/25231.png"
                        alt="GitHub Logo"
                        className="object-contain w-full h-full"
                    />
                </div>

                <div className="ml-4">
                    <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">{title}</h2>
                    <p className="typography-body-sm text-semantic-gray-500">
                        ID: {id} • Last synced {lastSynced} ago
                    </p>
                </div>
            </div>

            <div className="flex items-center space-x-4">
                {isConnected ? (
                    <div className="bg-green-500 w-5 h-5 rounded-full flex items-center justify-center">
                        <FaCheck size={12} color="white" />
                    </div>
                ) : (
                    <div className="bg-red-500 w-5 h-5 rounded-full flex items-center justify-center">
                        <FaExclamation size={12} color="white" />
                    </div>
                )}
                {/* <button className="p-2 rounded-lg border border-semantic-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200">
                    <FaEllipsisH size={16} />
                </button> */}
                <Menu as="div" className="relative">
                    <Menu.Button className="p-2 rounded-lg border border-semantic-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200"
                        onClick={(e) => e.stopPropagation()}>
                        <FaEllipsisH size={10} />
                    </Menu.Button>
                    <Menu.Items className="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg">
                        {connectedSettingsArray && connectedSettingsArray.map((item, index) => (
                            <Menu.Item key={index}>
                                {({ active }) => (
                                    <button
                                        className={`block w-full text-left px-4 py-2 ${active ? 'bg-semantic-gray-100' : ''}`}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            _handleMenuClick(item)
                                        }}
                                    >
                                        {item}
                                    </button>
                                )}
                            </Menu.Item>
                        ))}
                    </Menu.Items>
                </Menu>
            </div>
        </div>
    );
};

export default AccountDetailCard;
