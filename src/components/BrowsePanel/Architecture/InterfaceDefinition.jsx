import React, { useState } from 'react';
import { ChevronDown, Link2, FileText, Code, AlertTriangle, Co<PERSON>, Check } from 'lucide-react';

const InterfaceDefinition = ({ data }) => {
  const [expandedSections, setExpandedSections] = useState({});
  const [copiedStates, setCopiedStates] = useState({});

  const getIconForType = (type) => {
    switch (type) {
      case 'HttpRoute': return <Link2 className="w-4 h-4 text-indigo-600" />;
      case 'Method': return <Code className="w-4 h-4 text-purple-600" />;
      case 'DocumentationRoot': return <FileText className="w-4 h-4 text-primary-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    }
  };

  const copyToClipboard = async (text, id) => {
    await navigator.clipboard.writeText(text);
    setCopiedStates(prev => ({ ...prev, [id]: true }));
    setTimeout(() => setCopiedStates(prev => ({ ...prev, [id]: false })), 2000);
  };

  const toggleSection = (sectionId) => {
    setExpandedSections(prev => ({ ...prev, [sectionId]: !prev[sectionId] }));
  };

  const renderMarkdown = (content) => {
    if (!content) return null;

    // Remove "LLM response:" prefix if present
    content = content.replace('LLM response:', '').trim();

    const lines = content.split('\n');
    const elements = [];
    let inCodeBlock = false;
    let codeContent = '';
    let codeLanguage = '';
    let listItems = [];
    let inBold = false;

    const renderListItems = () => {
      if (listItems.length > 0) {
        elements.push(
          <ul key={`list-${elements.length}`} className="list-disc pl-6 my-2 space-y-1">
            {listItems.map((item, idx) => (
              <li key={idx} className="text-semantic-gray-700">{item}</li>
            ))}
          </ul>
        );
        listItems = [];
      }
    };

    const processLine = (line) => {
      // Handle inline code with backticks
      const parts = line.split('`');
      return parts.map((part, index) => {
        // If it's an odd index, it's inside backticks
        if (index % 2 === 1) {
          return <code key={index} className="px-1.5 py-0.5 rounded bg-semantic-gray-100 text-semantic-gray-800  typography-body-sm">{part}</code>;
        }

        // Handle bold text with asterisks for non-code parts
        return part.split('**').map((boldPart, boldIndex) => {
          inBold = !inBold;
          return boldIndex % 2 === 1 ?
            <strong key={`${index}-${boldIndex}`} className="font-weight-semibold">{boldPart}</strong> :
            boldPart;
        });
      });
    };

    lines.forEach((line, idx) => {
      // Add handling for horizontal rule
      if (line.trim() === '---') {
        renderListItems(); // Render any pending list items
        elements.push(
          <hr key={`hr-${idx}`} className="my-6 border-t border-semantic-gray-200" />
        );
        return;
      }

      const codeBlockRegex = /^\s*```(?:\s*(\w+))?\s*$/;
      const codeBlockMatch = line.match(codeBlockRegex);

      if (codeBlockMatch) {
        if (inCodeBlock) {
          // Closing code block
          const blockId = `code-${idx}`;
          elements.push(
            <div key={blockId} className="relative group">
              <pre className="bg-white text-semantic-gray-800 p-4 rounded-lg my-4 overflow-x-auto  typography-body-sm border border-semantic-gray-200">
                <code className={`language-${codeLanguage}`}>{codeContent.trim()}</code>
              </pre>
              <button
                onClick={() => copyToClipboard(codeContent.trim(), blockId)}
                className="absolute top-2 right-2 p-2 opacity-0 group-hover:opacity-100 transition-opacity bg-semantic-gray-100 rounded-md hover:bg-semantic-gray-200"
              >
                {copiedStates[blockId] ?
                  <Check className="w-4 h-4 text-green-400" /> :
                  <Copy className="w-4 h-4 text-semantic-gray-500" />
                }
              </button>
            </div>
          );
          inCodeBlock = false;
          codeContent = '';
          codeLanguage = '';
        } else {
          // Starting code block
          inCodeBlock = true;
          codeLanguage = codeBlockMatch[1] || '';
        }
        return;
      }

      if (inCodeBlock) {
        codeContent += line + '\n';
        return;
      }

      renderListItems();

      if (line.startsWith('# ')) {
        elements.push(
          <h1 key={idx} className="typography-heading-1 font-weight-bold mt-8 mb-4 text-semantic-gray-900">
            {processLine(line.slice(2))}
          </h1>
        );
      } else if (line.startsWith('## ')) {
        elements.push(
          <h2 key={idx} className="typography-heading-2 font-weight-semibold mt-6 mb-3 text-semantic-gray-800">
            {processLine(line.slice(3))}
          </h2>
        );
      } else if (line.startsWith('### ')) {
        elements.push(
          <h3 key={idx} className="typography-heading-4 font-weight-medium mt-4 mb-2 text-semantic-gray-700">
            {processLine(line.slice(4))}
          </h3>
        );
      } else if (line.startsWith('- ')) {
        listItems.push(processLine(line.slice(2)));
      } else if (line.trim().length > 0) {
        elements.push(
          <p key={idx} className="my-2 text-semantic-gray-600 leading-relaxed">
            {processLine(line)}
          </p>
        );
      }
    });

    renderListItems();
    return elements;
  };

  const renderDocumentationRoot = (item) => (
    <div className="bg-white rounded-xl shadow-sm border border-semantic-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-semantic-gray-50 to-white border-b border-semantic-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary-50 rounded-lg">
              <FileText className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h2 className="typography-heading-4 font-weight-bold text-semantic-gray-900">{item.Title}</h2>
              <div className="flex items-center mt-1 space-x-2 typography-body-sm text-semantic-gray-500">
                <span>Version {item.Version}</span>
                <span>•</span>
                <span>Updated {new Date(item.LastUpdated).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {item.labels.map((label, idx) => (
              <span key={idx} className="px-3 py-1 typography-body-sm font-weight-medium rounded-full bg-primary-50 text-primary-700">
                {label}
              </span>
            ))}
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="prose prose-orange max-w-none">
          {renderMarkdown(item.Content)}
        </div>
        {item.PublicAPIDetails && (
          <EndpointDisplay
            endpoints={JSON.parse(item.PublicAPIDetails.replace(/<\/?JSON>/g, '')).paths}
          />
        )}
      </div>
    </div>
  );

  const RouteCard = ({ item, type }) => {
    const sectionId = `${type}-${item.id}`;
    const isExpanded = expandedSections[sectionId] ?? true;

    const getBadgeColor = () => {
      if (type === 'HttpRoute') return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      return 'bg-purple-50 text-purple-700 border-purple-200';
    };

    const sortedEntries = Object.entries(item).sort(([keyA], [keyB]) => {
      if (keyA === 'Title') return -1;
      if (keyB === 'Title') return 1;
      return 0;
    });


    return (
      <div className="bg-white rounded-lg shadow-sm border border-semantic-gray-200 hover:shadow-md transition-all duration-200">
        <button
          onClick={() => toggleSection(sectionId)}
          className="w-full p-4 flex items-center justify-between border-b border-semantic-gray-200 hover:bg-semantic-gray-50 transition-colors"
        >
          <div className="flex items-center space-x-4">
            <div className={`p-2 rounded-lg ${type === 'HttpRoute' ? 'bg-indigo-50' : 'bg-purple-50'}`}>
              {getIconForType(type)}
            </div>
            <div className="flex items-center justify-between  space-x-2">
              <h3 className="font-weight-semibold text-semantic-gray-900">{item.Title}</h3>
              <div className="flex items-center mt-1">
                <span className={`px-2 py-0.5 typography-caption font-weight-medium rounded-full border ${getBadgeColor()}`}>
                  {type}
                </span>
                {/* <code className="typography-body-sm text-semantic-gray-600">
                  {type === 'HttpRoute' ? item.Route : item.Signature}
                </code> */}
              </div>
            </div>
          </div>
          <div className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
            <ChevronDown className="w-5 h-5 text-semantic-gray-400" />
          </div>
        </button>

        {isExpanded && (
          <div className="p-4 space-y-4 bg-semantic-gray-50">
            {sortedEntries.map(([key, value]) => {
              if (key === 'id' || key === 'Type') return null;
              return (
                <div key={key} className="bg-white p-4 rounded-lg border border-semantic-gray-200">
                  <div className="typography-body-sm font-weight-medium text-semantic-gray-500 mb-2">{key}</div>
                  {Array.isArray(value) ? (
                    <div className="flex flex-wrap gap-2">
                      {value.map((label, idx) => (
                        <span key={idx} className="px-2 py-1 typography-caption font-weight-medium rounded-full bg-semantic-gray-100 text-semantic-gray-700">
                          {label}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <div className="text-semantic-gray-700 break-words">{value}</div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  const EndpointDisplay = ({ endpoints }) => {
    const [expandedEndpoint, setExpandedEndpoint] = useState(null);

    const getMethodColor = (method) => {
      const colors = {
        GET: "bg-primary-100 text-primary-700",
        POST: "bg-green-100 text-green-700",
        PUT: "bg-yellow-100 text-yellow-700",
        DELETE: "bg-red-100 text-red-700",
        PATCH: "bg-purple-100 text-purple-700",
      };
      return colors[method] || "bg-semantic-gray-100 text-semantic-gray-700";
    };

    return (
      <div className="mt-8 space-y-4">
        {endpoints.map((endpoint, index) => (
          <div key={index} className="border rounded-md overflow-hidden">
            <div
              className="flex items-center p-4 cursor-pointer hover:bg-semantic-gray-50"
              onClick={() => setExpandedEndpoint(expandedEndpoint === index ? null : index)}
            >
              <span className={`px-3 py-1 rounded-md typography-body-sm font-weight-medium ${getMethodColor(endpoint.HTTP_method)}`}>
                {endpoint.HTTP_method}
              </span>
              <span className="ml-4  typography-body-sm text-semantic-gray-700">{endpoint.Path}</span>
            </div>

            {expandedEndpoint === index && (
              <div className="border-t p-4 bg-semantic-gray-50">
                <div className="space-y-4">
                  {endpoint.Description && (
                    <div>
                      <h4 className="typography-body-sm font-weight-medium text-semantic-gray-700">Description</h4>
                      <p className="mt-1 typography-body-sm text-semantic-gray-600">{endpoint.Description}</p>
                    </div>
                  )}

                  {/* Parameters Section */}
                  {endpoint.parameters && endpoint.parameters.length > 0 && (
                    <div>
                      <h4 className="typography-body-sm font-weight-medium text-semantic-gray-700">Parameters</h4>
                      <div className="mt-2 overflow-x-auto">
                        <table className="min-w-full divide-y divide-semantic-gray-200">
                          <thead className="bg-semantic-gray-100">
                            <tr>
                              <th className="px-4 py-2 text-left typography-caption font-weight-medium text-semantic-gray-500">Name</th>
                              <th className="px-4 py-2 text-left typography-caption font-weight-medium text-semantic-gray-500">Type</th>
                              <th className="px-4 py-2 text-left typography-caption font-weight-medium text-semantic-gray-500">Required</th>
                              <th className="px-4 py-2 text-left typography-caption font-weight-medium text-semantic-gray-500">Description</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-semantic-gray-200">
                            {endpoint.parameters.map((param, idx) => (
                              <tr key={idx}>
                                <td className="px-4 py-2 typography-body-sm text-semantic-gray-900">{param.name}</td>
                                <td className="px-4 py-2 typography-body-sm text-semantic-gray-600">{param.type || param.schema?.type}</td>
                                <td className="px-4 py-2 typography-body-sm text-semantic-gray-600">{param.required ? "Yes" : "No"}</td>
                                <td className="px-4 py-2 typography-body-sm text-semantic-gray-600">{param.description || "-"}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* Request Body Schema */}
                  {endpoint.request_body && (
                    <div>
                      <h4 className="typography-body-sm font-weight-medium text-semantic-gray-700">Request Body Schema</h4>
                      <div className="mt-2 bg-white rounded-md p-3 border">
                        <pre className="typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(endpoint.request_body, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  {/* Response Schema */}
                  {endpoint.response_schema && (
                    <div>
                      <h4 className="typography-body-sm font-weight-medium text-semantic-gray-700">Response Schema</h4>
                      <div className="mt-2 bg-white rounded-md p-3 border">
                        <pre className="typography-body-sm text-semantic-gray-600 whitespace-pre-wrap">
                          {JSON.stringify(endpoint.response_schema, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const DataContract = ({ item }) => (
    <div className="bg-white rounded-xl shadow-sm border border-semantic-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-semantic-gray-50 to-white border-b border-semantic-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className="p-2 bg-primary-50 rounded-lg">
              <FileText className="w-6 h-6 text-primary-600" />
            </div>
            <div>
              <h2 className="typography-heading-4 font-weight-bold text-semantic-gray-900">{item.Title}</h2>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-2">
            {item.labels && item.labels.map((label, idx) => (
              <span key={idx} className="px-3 py-1 typography-body-sm font-weight-medium rounded-full bg-primary-50 text-primary-700">
                {label}
              </span>
            ))}
          </div>
        </div>
      </div>
      <div className="p-6">
        {item.DataContract && (
          <div className="mt-4 bg-semantic-gray-50 p-4 rounded-lg">
            <h3 className="typography-body-lg font-weight-semibold text-semantic-gray-800">Data Contract</h3>
            <p className="typography-body-sm text-semantic-gray-600">{item.DataContract}</p>
            <h4 className="typography-body-lg font-weight-semibold text-semantic-gray-800 mt-2">Schema</h4>
            <pre className="bg-semantic-gray-100 p-4 rounded-lg overflow-x-auto typography-body-sm">
              {JSON.stringify(JSON.parse(item.Schema), null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );



  return (
    <div className="space-y-8 p-6">
      {data.DataContract && (
        <div id="data-contract">
          {data.DataContract.map((item, index) => (
            <DataContract key={index} item={item} />
          ))}
        </div>
      )}

      {data.DocumentationRoot && (
        <div id ="documentation-root" className="mb-8">
          {data.DocumentationRoot.map(item => renderDocumentationRoot(item))}
        </div>
      )}

      {data.HttpRoute && (
        <div id='http-route'>
          <div className="flex items-center space-x-2 mb-4">
            <Link2 className="w-5 h-5 text-indigo-600" />
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-900">HTTP Routes</h2>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
            {data.HttpRoute.map(item => (
              <RouteCard key={item.id} item={item} type="HttpRoute" />
            ))}
          </div>
        </div>
      )}

      {data.Method && (
        <div id ="method">
          <div  className="flex items-center space-x-2 mb-4">
            <Code className="w-5 h-5 text-purple-600" />
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-900">Methods</h2>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-2">
            {data.Method.map(item => (
              <RouteCard key={item.id} item={item} type="Method" />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default InterfaceDefinition;