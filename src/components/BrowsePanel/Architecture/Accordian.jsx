// components/Accordion.js
"use client"
import React, { useEffect, useState, useRef, useImperativeHandle, forwardRef } from "react";
import { ChevronDown } from 'lucide-react';

const Accordion = forwardRef(({ 
  title, 
  children, 
  defaultOpen = false,
  id,
  isAccordionOpen,
  onToggle,
  headerExtra,
  className = ''
}, ref) => {
  const [isOpen, setIsOpen] = useState(isAccordionOpen || defaultOpen);
  const localRef = useRef(null);

  useEffect(() => {
    setIsOpen(isAccordionOpen); // Ensure state syncs with prop
  }, [isAccordionOpen]);

  useImperativeHandle(ref, () => localRef.current);

  useEffect(() => {
    setIsOpen(defaultOpen);
  }, [defaultOpen]);
  
  return (
    <div id={id} ref={ref || localRef} className={`border rounded-lg overflow-hidden ${className}`}
      style={{ scrollMarginTop: '1rem' }}
    >
      <div
        className="flex justify-between items-center p-4 cursor-pointer bg-semantic-gray-50 hover:bg-semantic-gray-100"
        onClick={onToggle? onToggle : () => setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex-grow">{title}</div>
          <div className="flex items-center gap-4">
            {headerExtra && headerExtra(isOpen)}
            <ChevronDown
              className={`transform transition-transform ${
                isOpen ? 'rotate-180' : ''
              }`}
              size={20}
            />
          </div>
        </div>
      </div>
      {isOpen && <div className="p-4 bg-white">{children}</div>}
    </div>
  );
});

Accordion.displayName = 'Accordion';
export default Accordion;