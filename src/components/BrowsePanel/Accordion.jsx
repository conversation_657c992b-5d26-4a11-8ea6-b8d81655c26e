"use client";
import React, { useState, useEffect } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { renderHTML } from "@/utils/helpers";

const Accordion = ({ 
  title, 
  content, 
  preview, 
  renderToHtml = false, 
  defaultOpen = true,
  children 
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [shouldUseAccordion, setShouldUseAccordion] = useState(false);

  useEffect(() => {
    if (content) {
      const lineCount = content.split('\n').length;
      const wordCount = content.split(/\s+/).length;
      setShouldUseAccordion(lineCount > 5 || wordCount > 94 || isTechStack);
    } else {
      setShouldUseAccordion(true);
    }
  }, [content]);

  const makeHumanReadable = (str) => {
    return str
      ?.replace(/([a-z])([A-Z])/g, '$1 $2')
      ?.replace(/_/g, ' ')
      ?.replace(/-/g, ' ')
      ?.replace(/\b\w/g, char => char.toUpperCase());
  };
  
  const modifiedPreview = preview ? makeHumanReadable(preview) : '';

  // Removed formatTechStack - let renderHTML handle all markdown formatting consistently

  const isTechStack = title === "Tech Stack Choices" || title === "Recommended Tech Stack";

  return (
    <div className="mt-3 border rounded-lg">
      <button
        className="flex items-center justify-between w-full p-3 hover:bg-semantic-gray-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="project-panel-heading">{title}</span>
        {isOpen ? 
          <FaChevronUp className="text-color icon-font" /> : 
          <FaChevronDown className="text-color icon-font" />
        }
      </button>

      {isOpen && (
        <div className="p-3 border-t">
          {children ? children : (
            renderToHtml ? (
              <span dangerouslySetInnerHTML={{ __html: renderHTML(content) }}></span>
            ) : (
              content
            )
          )}
        </div>
      )}
    </div>
  );
};

export default Accordion;