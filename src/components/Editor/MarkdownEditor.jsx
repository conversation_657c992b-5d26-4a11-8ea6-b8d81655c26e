// components/MarkdownEditor.tsx

import React, { useState, useEffect, memo } from 'react';
import dynamic from 'next/dynamic';
import { AlertTriangle, X } from 'lucide-react';
import 'react-quill/dist/quill.snow.css';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import { renderHTML } from '@/utils/helpers';
import mermaid from 'mermaid';

// Debounce utility function
const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Dynamically import ReactQuill and CodeMirror
const ReactQuill = dynamic(() => import('react-quill'), {
  ssr: false,
  loading: () => <p>Loading editor...</p>,
});
const CodeMirror = dynamic(() => import('react-codemirror2').then(mod => mod.Controlled), {
  ssr: false,
  loading: () => <p>Loading code editor...</p>,
});

const MarkdownEditor = ({
  isOpen,
  onClose,
  content,
  onSave,
  title = 'Edit Content',
  displayType = 'rich_text',
}) => {
  const [editContent, setEditContent] = useState(content || '');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState(null);

  // Initialize Mermaid
  useEffect(() => {
    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    });
  }, []);

  // Update editContent when content prop changes
  useEffect(() => {
    if (isOpen && content !== undefined) {
      if (displayType === 'mermaid_chart') {
        setEditContent(content || '');
      } else {
        const htmlContent = renderHTML(content || '');
        setEditContent(htmlContent || '');
      }
    }
  }, [isOpen, content, displayType]);

  // Live validation for Mermaid syntax
  useEffect(() => {
    if (displayType === 'mermaid_chart' && editContent) {
      const validateMermaid = debounce(async () => {
        try {
          await mermaid.parse(editContent);
          setError(null);
        } catch (err) {
          setError(err.message || 'Invalid Mermaid syntax.');
        }
      }, 500);
      validateMermaid();
    } else {
      setError(null);
    }
  }, [editContent, displayType]);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered' }, { 'list': 'bullet' }],
      ['link'],
      ['clean']
    ]
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      if (displayType === 'mermaid_chart') {
        try {
          await mermaid.parse(editContent);
          setError(null);
        } catch (err) {

        }
      }
      await onSave(editContent);
      onClose();
    } catch (error) {

    } finally {
      setIsSaving(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="typography-body-lg font-weight-semibold">{title}</h3>
          <button onClick={onClose} className="p-1.5 hover:bg-semantic-gray-100 rounded">
            <X size={16} />
          </button>
        </div>
        <div className="p-4">
          {displayType === 'mermaid_chart' ? (
            <div className="relative h-[460px]">
              <CodeMirror
                key={`codemirror-${title}`}
                value={editContent}
                options={{
                  mode: 'mermaid',
                  theme: 'material',
                  lineNumbers: true,
                  lineWrapping: true,
                  gutters: ['CodeMirror-lint-markers'],
                }}
                editorDidMount={(editor) => {
                  editor.setSize('100%', error ? '380px' : '460px');
                }}
                onBeforeChange={(editor, data, value) => setEditContent(value)}
                className="code-mirror-container absolute top-0 left-0 w-full"
              />
              {error && (
                <div className="text-red-500 mt-2 absolute bottom-0 left-0 w-full max-h-[9rem] overflow-y-auto">
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                    <div className="flex items-start space-x-3">
                      <AlertTriangle className="text-red-500 mt-0.5 flex-shrink-0" size={18} />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h4 className="text-red-700 text-sm font-semibold tracking-tight">
                            Mermaid Syntax Error
                          </h4>
                        </div>
                        <p className="text-red-600 text-sm mt-1 leading-relaxed line-clamp-4">
                          {error || 'An error occurred in the Mermaid syntax.'}
                          {error.line ? <span className="ml-1 font-medium">Line: {error.line}</span> : ''}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <ReactQuill
              theme="snow"
              value={editContent}
              onChange={setEditContent}
              modules={modules}
              className="h-[400px] mb-12"
            />
          )}
        </div>

        <div className="p-4 border-t flex justify-between items-center">
          <div className="typography-caption text-semantic-gray-500">
            {displayType === 'mermaid_chart' ? (
              <span>Editing Mermaid chart. Ensure valid syntax.</span>
            ) : (
              <>
                <span className="mr-4">Ctrl/⌘ + B Bold</span>
                <span className="mr-4">Ctrl/⌘ + I Italic</span>
                <span>Ctrl/⌘ + K Link</span>
              </>
            )}
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 text-semantic-gray-600 hover:text-semantic-gray-800 border rounded"
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 disabled:bg-primary-300"
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(MarkdownEditor);