import React, { useState, useRef, useEffect } from 'react';
import IntegrationCard from './Cards';
import { FaSearch } from 'react-icons/fa';

interface CardListProps {
    title?: any;
    isMenu?: boolean;
    isSearch?: boolean;
    listArray?: any[];
    onCardClick: (cardData: any) => void;
}

const CardList: React.FC<CardListProps> = ({ title, isSearch, listArray, onCardClick }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [showDropdown, setShowDropdown] = useState<any>(false);
    const [selectedApp, setSelectedApp] = useState<any>(null);

    const dropdownRef = useRef(null);

    const filteredSuggestions = listArray?.filter(integration =>
        integration.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleSuggestionClick = (integration: any) => {
        setSearchTerm(integration.title);
        setSelectedApp(integration);
        setShowDropdown(false);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !(dropdownRef.current as any).contains(event.target)) {
                setShowDropdown(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [dropdownRef]);

    return (
        <div className="container mx-auto py-8 px-4">
            <div className="flex justify-between items-center mb-6">
                <h1 className="typography-heading-2 font-weight-bold">{title}</h1>

                {isSearch && (
                    <div className="relative w-full sm:w-1/3" ref={dropdownRef}>
                        <div className="relative">
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => {
                                    setSearchTerm(e.target.value);
                                    setSelectedApp(null);
                                }}
                                placeholder="Search Apps"
                                className="w-full pl-12 py-2 bg-white text-semantic-gray-500 border border-semantic-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-semantic-gray-200"
                                onClick={() => setShowDropdown(true)}
                            />
                            <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-semantic-gray-400">
                                <FaSearch size={20} />
                            </span>
                        </div>
                        {showDropdown && (
                            <ul className="absolute bg-white border border-semantic-gray-300 rounded-md mt-1 w-full max-h-48 overflow-y-auto z-10">
                                {filteredSuggestions && filteredSuggestions.map((integration, index) => (
                                    <li
                                        key={index}
                                        className="flex items-center p-2 hover:bg-semantic-gray-100 cursor-pointer"
                                        onClick={() => handleSuggestionClick(integration)}
                                    >
                                        <img
                                            src={integration.imgSrc}
                                            alt={integration.title}
                                            className="w-6 h-6 mr-2 object-contain"
                                        />
                                        {integration.title}
                                    </li>
                                ))}
                            </ul>
                        )}
                    </div>
                )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {selectedApp ? (
                    <IntegrationCard
                        key={selectedApp.title}
                        title={selectedApp.title}
                        description={selectedApp.description}
                        imgSrc={selectedApp.imgSrc}
                        isConnected={selectedApp.isConnected}
                        isMenu={selectedApp.isMenu}
                        connectedSettingsArray={selectedApp.connectedSettingsArray}
                        onCardClick={() => onCardClick(selectedApp)}
                    />
                ) : (
                    filteredSuggestions && filteredSuggestions.map((integration, index) => (
                        <IntegrationCard
                            key={index}
                            title={integration.title}
                            description={integration.description}
                            isMenu={integration.isMenu}
                            imgSrc={integration.imgSrc}
                            isConnected={integration.isConnected}
                            connectedSettingsArray={integration.connectedSettingsArray}
                            onCardClick={() => onCardClick(integration)}
                        />
                    ))
                )}
            </div>
        </div>
    );
};

export default CardList;
