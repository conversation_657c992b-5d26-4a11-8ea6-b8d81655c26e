"use client";
import React, { useState, useContext, useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { forgotPasswordRequest } from "../../../../utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { decryptTenantId, encryptedTenantId, getRootTenantId } from "@/utils/hash";
import LoginSignupContainer from "@/components/LoginSignupContainer";

const ForgotPasswordPage = () => {
  const queryParams = useSearchParams();
  const [tenant_id, setTenant_id] = useState(null);
  const [email, setEmail] = useState("");
  const { showAlert } = useContext(AlertContext)
  const [buttonStatus, setButtonStatus] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (queryParams.get("tenant_id")) {
      if (decryptTenantId(queryParams.get("tenant_id"))) {
        setTenant_id(queryParams.get("tenant_id"));
      } else {
        showAlert("Invalid tenant ID.", "danger");
      }
    } else {
      // Only redirect if this page is being accessed directly (not as part of a redirect flow)
      const referrer = document.referrer;
      const isDirectAccess = !referrer || !referrer.includes(window.location.hostname);

      if (isDirectAccess) {
        router.push("/users/login?tenant_id=" + encodeURIComponent(getRootTenantId()));
      }
      setTenant_id(encryptedTenantId())
    }
  }, [queryParams]);

  const handleSubmit = async (event) => {
    setButtonStatus(true);
    event.preventDefault();

    if (!tenant_id) {
      showAlert("Tenant ID is required.", "danger");
      setButtonStatus(false);
      return;
    }

    if (!email) {
      showAlert("Email is required.", "danger");
      setButtonStatus(false);
      return;
    }

    try {
      const response = await forgotPasswordRequest(email, tenant_id);

      showAlert("Password reset request sent successfully!", "success");
      setButtonStatus(false);
      setTimeout(() => {
        router.push(
          `/users/confirm_forgot_password?email=${encodeURIComponent(email)}&tenant_id=${encodeURIComponent(tenant_id)}`
        );
      }, 2000);
    } catch (error) {
      
      showAlert(
        "Trying to reset the password for the E-mail that doesn't exists!"
        , "danger");
      setButtonStatus(false);
    }
  };


  return (
    <LoginSignupContainer>
      <div className="bg-white rounded-lg shadow-xl -mt-28 p-8 max-w-md w-full z-10 text-center">
        <h1 className="user-panel-sub-head mb-2">Forgot your password?</h1>
        <p className="text-font mb-6 mt-5">
          Please enter the email address you&apos;d like your password reset
          information sent to.
        </p>
        <form onSubmit={handleSubmit} className="flex flex-col items-center">
          <label
            htmlFor="email"
            className="self-start mb-2 text-semantic-gray-700 typography-heading-4"
          >
            Email Address <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Email Address"
            required
            className="w-full px-4 py-2 mb-4 border border-semantic-gray-300 rounded-md"
          />
          <button
            type="submit"
            disabled={buttonStatus}
            className={`w-full bg-[hsl(var(--primary))] border border-[#8C3E10] border-opacity-50 mt-2 text-white rounded-md px-4 py-2.5 typography-body-lg ${buttonStatus ? 'bg-semantic-gray-400' : 'bg-[hsl(var(--primary))]'}`}
          >
            {!buttonStatus ? (
              <>
                Reset your password
              </>
            ) : (
              <div
                className=" inline-block h-6 w-6 animate-spin rounded-full border-4 border-solid border-current border-e-transparent align-[-0.125em] text-white"
                role="status"
              >
                <span className="absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">
                  Loading...
                </span>
              </div>
            )}

          </button>
        </form>
        <p className="mt-6 typography-body-lg text-semantic-gray-600">
          Have an account?{" "}
          <Link href="/users/login" className="text-[hsl(var(--primary))]">
            Sign in
          </Link>
        </p>
      </div>
    </LoginSignupContainer>
  );
};

export default ForgotPasswordPage;
