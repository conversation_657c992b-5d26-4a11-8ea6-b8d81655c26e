'use client';

import React, { useState, useEffect } from 'react';
import { Document, Page, } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { pdfjs } from 'react-pdf';
import { downloadDocument } from '@/utils/documentationAPI';
import { CircularProgress, IconButton } from '@mui/material';
import { 
  ArrowLeft as ArrowBack,
  ZoomIn,
  ZoomOut,
  ChevronRight as NavigateNext,
  ChevronLeft as NavigateBefore,
  ChevronLeft as KeyboardArrowLeft,
  ChevronRight as KeyboardArrowRight
} from 'lucide-react';

// Import PDF worker


// pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';

// pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const DocumentViewer = ({ document, onBack }) => {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  // Add keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowRight') {
        handleNextPage();
      } else if (e.key === 'ArrowLeft') {
        handlePreviousPage();
      } else if (e.key === '+' || e.key === '=') {
        handleZoomIn();
      } else if (e.key === '-') {
        handleZoomOut();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [pageNumber, numPages]);

  // Load PDF document
  useEffect(() => {
    const loadPDF = async () => {
      try {
        setLoading(true);
        setError(null);

        const blob = await downloadDocument(
          document.projectId,
          document.docType,
          document.version,
          document.fileName,
          document.folder_path
        );

        if (!(blob instanceof Blob)) {
          throw new Error('Invalid response format');
        }

        const url = URL.createObjectURL(blob);
        setPdfUrl(url);
      } catch (err) {
        
        setError('Failed to load the document. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (document) {
      loadPDF();
    }

    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [document]);

  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
    setPageNumber(1);
  }

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale + 0.1, 2.0));
  };

  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale - 0.1, 0.5));
  };

  const handlePreviousPage = () => {
    setPageNumber((prevPage) => Math.max(prevPage - 1, 1));
  };

  const handleNextPage = () => {
    setPageNumber((prevPage) => Math.min(prevPage + 1, numPages || prevPage));
  };

  const handlePageInputChange = (e) => {
    const value = parseInt(e.target.value);
    if (!isNaN(value) && value >= 1 && value <= numPages) {
      setPageNumber(value);
    }
  };

  const handleUploadToConfluence = () => {
    
    handleMenuClose();
  };

  if (loading) {
    return (
      <div className="flex flex-col h-screen items-center justify-center bg-white">
        <CircularProgress />
        <p className="mt-4 text-semantic-gray-600">Loading document...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col h-screen items-center justify-center bg-white">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={onBack}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Header */}
      <div className="flex items-center justify-between bg-white border-b px-4 py-2">
        <button 
          onClick={onBack} 
          className="text-primary hover:text-primary-800 flex items-center"
        >
          <ArrowBack className="mr-1" />
          <span>Back</span>
        </button>
        {/* <div className="flex items-center space-x-2">
          <button
            className="px-4 py-1.5 bg-primary text-white rounded hover:bg-primary-600"
            onClick={() => {}} // Implement update functionality
          >
            Update
          </button>
          <IconButton
            onClick={handleMenuClick}
            size="small"
            className="hover:bg-semantic-gray-100"
          >
            <MoreVert />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={open}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'document-options-button',
            }}
          >
            <MenuItem onClick={handleUploadToConfluence}>
              Upload to Confluence
            </MenuItem>
          </Menu>
        </div> */}
      </div>

      {/* PDF Controls */}
      <div className="bg-[#2D3748] text-white px-4 py-2 flex justify-between items-center sticky top-0 z-10">
        <h1 className="typography-body-lg font-weight-semibold truncate">
          {document.fileName}
        </h1>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <IconButton 
              onClick={handlePreviousPage}
              disabled={pageNumber <= 1}
              size="small"
              className="text-white hover:bg-semantic-gray-700 disabled:opacity-50"
            >
              <KeyboardArrowLeft className="text-white"/>
            </IconButton>
            <div className="flex items-center space-x-1">
              <input
                type="number"
                value={pageNumber}
                onChange={handlePageInputChange}
                min={1}
                max={numPages}
                className="w-16 bg-semantic-gray-700 text-white px-2 py-1 rounded text-center"
              />
              <span className="typography-body-sm">/ {numPages}</span>
            </div>
            <IconButton
              onClick={handleNextPage}
              disabled={pageNumber >= (numPages || 1)}
              size="small"
              className="text-white hover:bg-semantic-gray-700 disabled:opacity-50"
            >
              <KeyboardArrowRight className="text-white" />
            </IconButton>
          </div>
          <div className="flex items-center space-x-2">
            <IconButton 
              onClick={handleZoomOut}
              size="small"
              className="text-white hover:bg-semantic-gray-700"
            >
             <ZoomOut className="text-white" />
            </IconButton>
            <span className="typography-body-sm w-16 text-center">
              {Math.round(scale * 100)}%
            </span>
            <IconButton
              onClick={handleZoomIn}
              size="small"
              className="text-white hover:bg-semantic-gray-700"
            >
             <ZoomIn className="text-white" />
            </IconButton>
          </div>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="flex-1 max-h-[56vh] custom-scrollbar overflow-auto bg-semantic-gray-100">
        <div className="p-8 flex justify-center min-h-0">
          {pdfUrl && (
            <Document
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={(error) => {
                
                setError('Failed to load the document. Please try again.');
              }}
              loading={
                <div className="flex items-center justify-center">
                  <CircularProgress />
                </div>
              }
              className="shadow-lg"
            >
              <Page
                pageNumber={pageNumber}
                scale={scale}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                loading={
                  <div className="flex items-center justify-center">
                    <CircularProgress />
                  </div>
                }
                className="bg-white"
              />
            </Document>
          )}
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="flex justify-center space-x-4 p-4 bg-white border-t">
        <button
          onClick={handlePreviousPage}
          disabled={pageNumber <= 1}
          className="px-4 py-2 bg-primary text-white rounded disabled:bg-semantic-gray-300 disabled:cursor-not-allowed flex items-center"
        >
          <NavigateBefore />
          Previous
        </button>
        <button
          onClick={handleNextPage}
          disabled={pageNumber >= (numPages || 1)}
          className="px-4 py-2 bg-primary text-white rounded disabled:bg-semantic-gray-300 disabled:cursor-not-allowed flex items-center"
        >
          Next
          <NavigateNext />
        </button>
      </div>
    </div>
  );
};

export default DocumentViewer;