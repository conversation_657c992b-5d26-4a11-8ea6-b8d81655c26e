"use client";
import React, { useState } from "react";
import Editor from "react-simple-code-editor";
import { highlight, languages } from "prismjs/components/prism-core";
import "prismjs/components/prism-clike";
import "prismjs/components/prism-javascript";
import "prismjs/themes/prism-tomorrow.css";
import { Maximize2 } from "lucide-react";

const initialCode = `const host = Cypress.env("host");
let data;
import { generateProjectName } from "../fixtures/random";

describe('Create project spec', () => {
  const viewports = Cypress.env("viewport")?.lg;

  viewports?.forEach((size) => {
    describe(\`screen size - {size}\`, () => {
      beforeEach(() => {
        if (Cypress._.isArray(size)) {
          cy.viewport(size[0], size[1]);
        } else {
          cy.viewport(size);
        }
      })
    });
  });

  before(() => {
    cy.fixture('credentials').then((fixtureData) => {
      data = fixtureData;
    });
  });

  cy.visit(host.url);
});`;

const TestSpecTab = () => {
  const [code, setCode] = useState(initialCode);

  return (
    <div className="pb-16">
      <div className="p-4 bg-semantic-gray-900 rounded-lg">
        <div className="relative">
          {/* Wrapper div for setting max height and overflow */}
          <div className="max-h-[400px] overflow-y-auto">
            {/* Custom scrollbar styling */}
            <style jsx global>{`
              .editor-container::-webkit-scrollbar {
                width: 8px;
                height: 8px;
              }
              .editor-container::-webkit-scrollbar-track {
                background: hsl(var(--semantic-gray-800));
                border-radius: 4px;
              }
              .editor-container::-webkit-scrollbar-thumb {
                background: hsl(var(--semantic-gray-600));
                border-radius: 4px;
              }
              .editor-container::-webkit-scrollbar-thumb:hover {
                background: hsl(var(--semantic-gray-800));
              }
              .editor-container textarea {
                min-height: 400px !important;
              }
            `}</style>
            <div className="editor-container">
              <Editor
                value={code}
                onValueChange={(code) => setCode(code)}
                highlight={(code) => highlight(code, languages.js)}
                padding={10}
                style={{
                  fontFamily: 'Inter, monospace',
                  fontSize: 'var(--font-size-code)',
                  backgroundColor: "transparent",
                  color: "#A6ACCD",
                  height: "100%",
                  minHeight: "400px",
                }}
                className="w-full"
                textareaClassName="focus:outline-none"
              />
            </div>
          </div>
          <button className="absolute top-2 right-2 text-semantic-gray-400 hover:text-white transition-colors duration-200">
            <Maximize2 size={18} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestSpecTab;
