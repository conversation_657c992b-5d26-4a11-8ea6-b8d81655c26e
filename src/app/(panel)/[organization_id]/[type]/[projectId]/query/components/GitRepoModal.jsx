import React, { useState } from 'react';
import { IoMdClose } from 'react-icons/io';
import PersonalRepositorySection from "@/components/Repository/PersonalRepositorySection";
import { useProjectAsset } from '@/components/Context/ProjectAssetContext';
import { BuildProgressProvider } from '@/components/Context/BuildProgressContext';
import { WebSocketProvider } from "@/components/Context/WebsocketContext";
import { ProjectAssetProvider } from "@/components/Context/ProjectAssetContext";
import { ChevronRight } from 'lucide-react';

const GitRepoModal = ({ isOpen, onClose, onImport, repositoriesData, handleRefresh, updateBuildStatuses }) => {
  const { setRepositoryDetails, setScanningRepoId } = useProjectAsset();
  const [showRepositories, setShowRepositories] = useState(false);
  const [repositories, setRepositories] = useState([]);
  const [selectedRepositories, setSelectedRepositories] = useState([]);
  const [buttonEnabled, setButtonEnabled] = useState(false);

  const handleGitHubImport = async (repositories) => {
    try {
      if (!Array.isArray(repositories) || repositories.length === 0) return;

      const processedRepos = repositories.map(repo => ({
        name: repo.name,
        branch: repo.default_branch || repo.branch || 'main',
        clone_url: repo.clone_url || repo.git_url,
        provider: "github",
        language: repo.language,
        description: repo.description,
        status: repo.status || 'Not Started',
        repo_type: repo.repo_type || 'public'
      }));

      setRepositoryDetails(processedRepos);
      setRepositories(processedRepos);
      setShowRepositories(true); // Add this line to show repositories after import
      await onImport(processedRepos);
      onClose();
       // Add reload after a short delay to ensure state updates are complete
      setTimeout(() => {
          handleRefresh();
      }, 500);
    } catch (error) {

      setShowRepositories(false); // Hide on error
    }
  };

  const handleCheckboxChange = (repoId) => {
    setSelectedRepositories(prev => {
      if (prev.includes(repoId)) {
        return prev.filter(id => id !== repoId);
      }
      return [...prev, repoId];
    });
  };

  const handleBranchChange = (repoId, branch) => {
    setRepositories(prev =>
      prev.map(repo =>
        repo.id === repoId ? { ...repo, branch } : repo
      )
    );
  };

  const handleRemoveRepository = (repoId) => {
    setRepositories(prev => prev.filter(repo => repo.id !== repoId));
    setSelectedRepositories(prev => prev.filter(id => id !== repoId));
  };

  if (!isOpen) return null;

  const BreadCrumb = () => (
    <div className="flex-shrink-0 px-4 py-4 -mt-2 flex items-center space-x-3 typography-body-sm font-weight-medium text-semantic-gray-700">
      {showRepositories ? (
        <>
          <button
            className="flex items-center space-x-1 text-primary hover:underline hover:text-primary-700 transition"
            onClick={() => setShowRepositories(false)}
          >
            <span>Connection Details</span>
          </button>
          <ChevronRight className="h-4 w-4 text-semantic-gray-400" />
          <div className="flex items-center space-x-2">
            <p
              className="flex items-center space-x-1 text-semantic-gray-600 transition"
            >
              <span>Repositories List</span>
            </p>
            <ChevronRight className="h-4 w-4 text-semantic-gray-400" />
          </div>
        </>
      ) :(
        <>
          <p
            className="flex items-center space-x-1 text-semantic-gray-600 transition"
          >
            <span>Connection Details</span>
          </p>
          <ChevronRight className="h-4 w-4 text-semantic-gray-400" />
        </>
      )}
    </div>
  )

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="relative w-full max-w-[800px] mx-auto">
        <div className="bg-white rounded-lg shadow-2xl overflow-hidden pb-2">
          <div className="flex justify-between items-center p-4 ">
            <h2 className="typography-body-lg font-weight-semibold text-semantic-gray-800">
              Import Repository
            </h2>
            <button onClick={() => {setShowRepositories(false); onClose();}} className="text-semantic-gray-400 hover:text-semantic-gray-600">
              <IoMdClose size={24} />
            </button>
          </div>

          <BreadCrumb />

          <div className="flex-grow bg-white h-[70vh] overflow-hidden">
            <BuildProgressProvider>
              <WebSocketProvider>
                <ProjectAssetProvider>
                  <PersonalRepositorySection
                    onSCMTypeSelect={() => {}}
                    onImport={handleGitHubImport}
                    onClose={onClose}
                    addBtnClicked={true}
                    setShowRepositories={setShowRepositories}
                    repositories={repositoriesData}
                    showRepositories={showRepositories}
                    setRepositories={setRepositoryDetails}
                    setActiveTab={() => { }}
                    handleScanRepo={setScanningRepoId}
                    showDetails={false}
                    handleCloseModel={onClose}
                    handleRefresh={handleRefresh}
                    updateBuildStatuses={updateBuildStatuses}
                  />

                </ProjectAssetProvider>
              </WebSocketProvider>
            </BuildProgressProvider>

          </div>
        </div>
      </div>
    </div>
  );
};

export default GitRepoModal;