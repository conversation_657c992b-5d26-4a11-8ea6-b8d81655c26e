import React from 'react';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  
  return (
    <div className="flex items-center justify-between px-4 py-3 border-t border-semantic-gray-200">
      <div className="flex items-center">
        <p className="typography-body-sm text-semantic-gray-700">
          Showing page <span className="font-weight-medium">{currentPage}</span> of{' '}
          <span className="font-weight-medium">{totalPages}</span>
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 typography-body-sm rounded-md ${
            currentPage === 1
              ? 'text-semantic-gray-400 cursor-not-allowed'
              : 'text-semantic-gray-700 hover:bg-semantic-gray-100'
          }`}
        >
          Previous
        </button>
        
        <div className="flex items-center space-x-1">
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`px-3 py-1 typography-body-sm rounded-md ${
                currentPage === page
                  ? 'bg-primary-50 text-primary font-weight-medium'
                  : 'text-semantic-gray-700 hover:bg-semantic-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
        </div>

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 typography-body-sm rounded-md ${
            currentPage === totalPages
              ? 'text-semantic-gray-400 cursor-not-allowed'
              : 'text-semantic-gray-700 hover:bg-semantic-gray-100'
          }`}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default Pagination;