import React, { useState } from 'react';
import { 
  GitBranch, 
  GitPullRequest, 
  AlertCircle, 
  X, 
  Github
} from 'lucide-react';
import { Button, Switch, Tab, Tabs } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';

const theme = createTheme();

const GitDashboard = ({
  isVisible,
  onClose,
  currentRepository,
  githubUser,
  onCreatePullRequest,
  onViewIssues,
  onToggleAutoCommit,
  onToggleAutoPush,
  autoCommitEnabled,
  autoPushEnabled
}) => {
  const [activeTab, setActiveTab] = useState(0);
  
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Check if GitHub is connected
  const isGithubConnected = !!githubUser;

  return (
    <div 
      className={`absolute right-0 top-0 h-full w-[400px] bg-white shadow-xl transform transition-all duration-300 ease-in-out ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      } z-50 flex flex-col`}
      style={{ 
        position: 'absolute', 
        top: 0, 
        right: 0, 
        height: '100%', 
        width: '400px',
        visibility: isVisible ? 'visible' : 'hidden',
        transition: 'transform 0.3s ease-in-out, opacity 0.2s ease-in-out',
        transitionDelay: isVisible ? '0s' : '0.1s'
      }}
    >
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-3 border-b">
        <div className="flex items-center gap-2 text-semantic-gray-700">
          <Github size={24} />
          <span className="font-weight-medium typography-body-lg">Git Dashboard</span>
        </div>
        <button
          onClick={onClose}
          className="p-1.5 hover:bg-semantic-gray-100 rounded-full transition-colors"
          title="Close dashboard"
        >
          <X size={20} className="text-semantic-gray-600" />
        </button>
      </div>

      {/* GitHub Connection Status */}
      <div className={`mx-4 my-3 p-4 rounded-lg ${isGithubConnected ? 'bg-green-50' : ''}`}>
        <div className="flex items-center gap-2 mb-1">
          <Github size={20} className="text-semantic-gray-700" />
          <span className="font-weight-medium">{isGithubConnected ? 'GitHub Connected' : 'GitHub Not Connected'}</span>
        </div>
        {isGithubConnected ? (
          <div className="text-semantic-gray-600">
            Your account is connected to GitHub as <span className="font-weight-semibold">{githubUser}</span>
          </div>
        ) : (
          <div className="space-y-3">
            <p className="text-semantic-gray-600">
              Connect your GitHub account to use advanced Git features and collaborate more efficiently with your team.
            </p>
            <p className="text-semantic-gray-600">
              Connecting allows you to access repositories, create pull requests, and manage your code directly from this interface.
            </p>
            <ThemeProvider theme={theme}>
              <Button
                variant="contained"
                fullWidth
                startIcon={<Github size={18} />}
                sx={{
                  backgroundColor: '#ff5722',
                  '&:hover': {
                    backgroundColor: '#e64a19',
                  },
                  mt: 2,
                }}
              >
                Connect to GitHub
              </Button>
            </ThemeProvider>
          </div>
        )}
      </div>

      {/* Tabs */}
      <ThemeProvider theme={theme}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          aria-label="git dashboard tabs"
        >
          <Tab label="Repository Details" sx={{ 
            backgroundColor: activeTab === 0 ? '#1e2736' : '#ffffff',
            color: activeTab === 0 ? '#ffffff' : '#1e2736',
            fontWeight: 'medium',
            '&.Mui-selected': {
              color: '#ffffff',
            }
          }} />
          <Tab label="Git Commands" sx={{ 
            backgroundColor: activeTab === 1 ? '#1e2736' : '#ffffff',
            color: activeTab === 1 ? '#ffffff' : '#1e2736',
            fontWeight: 'medium',
            '&.Mui-selected': {
              color: '#ffffff',
            }
          }} />
        </Tabs>
      </ThemeProvider>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 0 && (
          <div className="space-y-6">
            {/* Current Repository */}
            <div>
              <h3 className="typography-body-lg font-weight-semibold border-b pb-2 mb-3">Current Repository</h3>
              {currentRepository ? (
                <>
                  <div className="typography-body-lg font-weight-medium text-semantic-gray-700 mb-1">
                    {currentRepository.organisation}/{currentRepository.name}
                  </div>
                  <div className="text-semantic-gray-600 mb-2">
                    {currentRepository.description || 'Custom code generation button implementation'}
                  </div>
                  <div className="typography-body-sm text-semantic-gray-500">
                    Last synced: {currentRepository.lastSynced || '10 mins ago'}, {currentRepository.activeBranches || '4'} active branches
                  </div>
                </>
              ) : (
                <div className="text-semantic-gray-500 italic">No repository selected</div>
              )}
            </div>

            {/* GitHub Actions */}
            <div>
              <h3 className="typography-body-lg font-weight-semibold border-b pb-2 mb-3">GitHub Actions</h3>
              <div className="space-y-2">
                <ThemeProvider theme={theme}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<GitPullRequest size={18} />}
                    onClick={onCreatePullRequest}
                    sx={{
                      backgroundColor: 'hsl(var(--card))',
                      '&:hover': {
                        backgroundColor: 'hsl(var(--muted))',
                      },
                    }}
                    disabled={!isGithubConnected}
                  >
                    Create Pull Request
                  </Button>
                  
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<AlertCircle size={18} />}
                    onClick={onViewIssues}
                    sx={{ mt: 1 }}
                    disabled={!isGithubConnected}
                  >
                    View Issues
                  </Button>
                </ThemeProvider>
              </div>
            </div>

            {/* Current Branch */}
            <div>
              <h3 className="typography-body-lg font-weight-semibold border-b pb-2 mb-3">Current Branch</h3>
              {currentRepository ? (
                <div className="bg-semantic-gray-50 border rounded-lg p-3 flex items-center gap-2">
                  <GitBranch size={18} className="text-semantic-gray-600" />
                  <span className=" text-semantic-gray-800">
                    {currentRepository.branch || 'feature/button-customization'}
                  </span>
                </div>
              ) : (
                <div className="text-semantic-gray-500 italic">No branch selected</div>
              )}
            </div>

            {/* Git Automation Settings */}
            <div>
              <h3 className="typography-body-lg font-weight-semibold border-b pb-2 mb-3">Git Automation Settings</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="text-semantic-gray-700 font-weight-medium">Auto-commit</span>
                  <ThemeProvider theme={theme}>
                    <Switch
                      checked={autoCommitEnabled}
                      onChange={onToggleAutoCommit}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#ff5722',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#ff5722',
                        },
                      }}
                    />
                  </ThemeProvider>
                </div>
                
                <div className="flex justify-between items-center py-3 border-b">
                  <span className="text-semantic-gray-700 font-weight-medium">Auto-push</span>
                  <ThemeProvider theme={theme}>
                    <Switch
                      checked={autoPushEnabled}
                      onChange={onToggleAutoPush}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: '#ff5722',
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: '#ff5722',
                        },
                      }}
                    />
                  </ThemeProvider>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 1 && (
          <div className="space-y-4">
            <div className="text-semantic-gray-600">
              Select a git command to run in the repository
            </div>
            {/* Git commands content would go here */}
          </div>
        )}
      </div>
    </div>
  );
};

export default GitDashboard;