// components/FigmaLinkingModal.jsx
import React, { useState, useEffect } from 'react';
import { FaS<PERSON>ch, FaSpinner, FaTimes, FaThLarge, FaList, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aEye } from 'react-icons/fa';
import Button from "@/components/Buttons";
import ImageLightbox from '@/components/Figma/ImageLightbox';
const FigmaLinkingModal = ({ isOpen, onClose, figmaFrames, initialSelectedFrames, onUpdateFrames }) => {
  const [selectedFrames, setSelectedFrames] = useState(initialSelectedFrames || []);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredFrames, setFilteredFrames] = useState(figmaFrames);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState('grid');
  const [lightboxImage, setLightboxImage] = useState(null);
  const [isSelected, setIsSelected] = useState(false);

  useEffect(() => {
    setFilteredFrames(
      figmaFrames.filter(frame => 
        frame.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [searchTerm, figmaFrames]);


  const openLightbox = (e, imageUrl) => {
    e.stopPropagation();
    setLightboxImage(imageUrl);
  };

  const closeLightbox = () => {
    setLightboxImage(null);
  };


  const renderGridItem = (frame) => (
    <div
      key={frame.id}
      className={`w-full h-64 border rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
        selectedFrames.includes(frame.id)
          ? 'border-primary shadow-lg ring-2 ring-primary-300'
          : 'border-semantic-gray-200 hover:shadow-md hover:border-primary-300'
      }`}
      onClick={() => handleFrameSelection(frame.id)}
    >
      <div className="relative h-40">
        {frame.imageUrl ? (
          <img
            src={frame.imageUrl}
            alt={frame.name}
            className="w-full h-full object-cover rounded-t-lg"
          />
        ) : (
          <div className="w-full h-full bg-semantic-gray-100 flex items-center justify-center text-semantic-gray-400 rounded-t-lg">
            No Image
          </div>
        )}
        <div className="absolute top-2 right-2 flex space-x-2">
          {frame.imageUrl && (
            <button
              onClick={(e) => openLightbox(e, frame.imageUrl)}
              className="bg-white rounded-full p-1 shadow-md hover:bg-semantic-gray-100 transition-colors"
            >
              <FaEye className="text-semantic-gray-600" />
            </button>
          )}
          {selectedFrames.includes(frame.id) && (
            <div className="bg-primary text-white rounded-full p-1">
              <FaCheck size={12} />
            </div>
          )}
        </div>
      </div>
      <div className="p-2">
        <p className="font-weight-medium truncate">{frame.name}</p>
        <p className="typography-caption text-semantic-gray-500 truncate">{frame.id}</p>
      </div>
    </div>
  );

  const renderListItem = (frame) => (
    <div 
      key={frame.id} 
      className={`flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 ${
        selectedFrames.includes(frame.id) ? 'bg-primary-100 border-primary border' : 'hover:bg-semantic-gray-100'
      }`}
    >
      <input
        type="checkbox"
        id={frame.id}
        checked={selectedFrames.includes(frame.id)}
        onChange={() => handleFrameSelection(frame.id)}
        className="mr-3 h-5 w-5 text-primary cursor-pointer"
      />
      <label htmlFor={frame.id} className="flex-grow cursor-pointer">
        <span className="font-weight-medium">{frame.name}</span>
        <span className="typography-body-sm text-semantic-gray-500 ml-2">({frame.id})</span>
      </label>
      {frame.imageUrl && (
        <button
          onClick={(e) => openLightbox(e, frame.imageUrl)}
          className="ml-2 bg-white rounded-full p-1 shadow-md hover:bg-semantic-gray-100 transition-colors"
        >
          <FaEye className="text-semantic-gray-600" />
        </button>
      )}
    </div>
  );

  const renderGrid = () => (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {filteredFrames.map(renderGridItem)}
    </div>
  );

  const renderList = () => (
    <div className="space-y-2">
      {filteredFrames.map(renderListItem)}
    </div>
  );

  if (!isOpen) return null;


  const renderMasonryGrid = () => {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-auto" style={{ gridAutoFlow: 'dense' }}>
        {filteredFrames.map((frame) => (
          <div
            key={frame.id}
            className={`border rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
              selectedFrames.includes(frame.id)
                ? 'border-primary shadow-lg ring-2 ring-primary-300'
                : 'border-semantic-gray-200 hover:shadow-md hover:border-primary-300'
            }`}
            onClick={() => handleFrameSelection(frame.id)}
            style={{ gridRowEnd: `span ${Math.ceil(Math.random() * 2) + 1}` }} // Randomize height for demonstration
          >
            <div className="relative h-0 pb-[56.25%]"> {/* 16:9 aspect ratio */}
              {frame.imageUrl ? (
                <img
                  src={frame.imageUrl}
                  alt={frame.name}
                  className="absolute top-0 left-0 w-full h-full object-cover rounded-t-lg"
                />
              ) : (
                <div className="absolute top-0 left-0 w-full h-full bg-semantic-gray-100 flex items-center justify-center text-semantic-gray-400 rounded-t-lg">
                  No Image
                </div>
              )}
              {selectedFrames.includes(frame.id) && (
                <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
                  <FaCheck size={12} />
                </div>
              )}
            </div>
            <div className="p-2">
              <p className="font-weight-medium truncate">{frame.name}</p>
              <p className="typography-caption text-semantic-gray-500 truncate">{frame.id}</p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const handleFrameSelection = (frameId) => {
    setSelectedFrames(prev => 
      prev.includes(frameId) ? prev.filter(id => id !== frameId) : [...prev, frameId]
    );
  };

  const handleUpdateFrames = async () => {
    setIsLoading(true);
    await onUpdateFrames(selectedFrames);
    setIsLoading(false);
    onClose();
  };

  const handleSelectAll = () => {
    setSelectedFrames(filteredFrames.map(frame => frame.id));
  };

  const handleDeselectAll = () => {
    setSelectedFrames([]);
  };


  const handleToggle = () => {
    if (!isSelected) {
      handleSelectAll();
    } else {
      handleDeselectAll();
    }
    setIsSelected((prev) => !prev);
  };


  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl w-[95%] max-w-5xl max-h-[90vh] flex flex-col">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center mb-4">
            <h2 className="typography-heading-2 font-weight-semibold">Link Figma Elements</h2>
            <button onClick={onClose} className="text-semantic-gray-500 hover:text-semantic-gray-700 transition-colors">
              <FaTimes size={24} />
            </button>
          </div>
          
          <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative w-full sm:w-auto flex-grow">
              <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                <FaSearch className="h-5 w-5 text-semantic-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search frames..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-8 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-primary placeholder-semantic-gray-400"
              />
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-2 rounded transition-colors ${viewMode === 'grid' ? 'bg-primary text-white' : 'bg-semantic-gray-200 text-semantic-gray-700 hover:bg-semantic-gray-300'}`}
              >
                <FaThLarge className="mr-2 inline" /> Grid
              </Button>
              <Button
                onClick={() => setViewMode('list')}
                className={`px-3 py-2 rounded transition-colors ${viewMode === 'list' ? 'bg-primary text-white' : 'bg-semantic-gray-200 text-semantic-gray-700 hover:bg-semantic-gray-300'}`}
              >
                <FaList className="mr-2 inline" /> List
              </Button>
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <span
              onClick={handleToggle}
              className="cursor-pointer typography-body-sm text-primary hover:underline"
            >
              {isSelected ? "Deselect All" : "Select All"}
            </span>
          </div>
        </div>

        <div className="flex-grow overflow-y-auto p-6">
          {viewMode === 'list' ?  renderList() : renderGrid() }
        </div>

        <div className="p-6 border-t flex justify-between items-center">
          <p className="typography-body-sm text-semantic-gray-600">
            {selectedFrames.length} frame{selectedFrames.length !== 1 ? 's' : ''} selected
          </p>
          <div className="flex space-x-2">
            <Button
              onClick={handleUpdateFrames}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              disabled={isLoading}
            >
              {isLoading ? (
                <><FaSpinner className="animate-spin mr-2 inline" /> Updating...</>
              ) : (
                'Update Linked Elements'
              )}
            </Button>
          </div>
        </div>
      </div>
      {lightboxImage && (
        <ImageLightbox imageUrl={lightboxImage} onClose={closeLightbox} />
      )}
    </div>
  );
};

export default FigmaLinkingModal;