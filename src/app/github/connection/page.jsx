"use client";
import React, { useState, useEffect } from 'react';
import { gitHubCallback } from "@/utils/gitAPI"; // Assuming gitHubCallback is defined in the utils
import Cookies from 'js-cookie';

const Page = () => {
  const [loading, setLoading] = useState(true);
  const [alertMessage, setAlertMessage] = useState(null);
  const [closingIn, setClosingIn] = useState(3);

  useEffect(() => {
    // Extract the 'code' parameter from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    
    if (code) {
      handleGitHubCallback(code);
    } else {
      setLoading(false); // If there's no code, proceed without showing loading
    }
  }, []);

  const handleGitHubCallback = async (code) => {
    try {
      // Call the API with the code and get the response
      const userId = Cookies.get('userId');    

      const git_user_response = await gitHubCallback(code, userId);
      // Log the response data
      // Save GitHub user data in localStorage
      localStorage.setItem('gitUserData', JSON.stringify(git_user_response));
      // localStorage.setItem('isGitHubConnected', 'true');

      // Remove 'code' from URL after successful connection
      const url = new URL(window.location.href);
      url.searchParams.delete('code');
      window.history.replaceState(null, '', url.toString());
      setLoading(false); // Hide loading after successful connection
      setInterval(() => {
        setClosingIn((prev) => prev - 1);
      }
      , 1000);
      setTimeout(() => {
        window.close();
      }, 3000);
    } catch (error) {
      
      setAlertMessage({
        title: 'Error',
        content: 'Failed to connect to GitHub.',
      });
      setLoading(false); // Hide loading on error
    }
  };

  return (
    <div className="flex justify-center items-center h-screen">
      {loading ? (
        <div className="card bg-semantic-gray-100 p-6 rounded-lg shadow-lg max-w-sm">
          <h2 className="typography-heading-4 font-weight-semibold text-center">Please Wait</h2>
          <p className="text-center mt-2">Your connection is being processed...</p>
        </div>
      ) : (
        <div className="text-center">
          <h2 className="typography-heading-4 font-weight-semibold">{alertMessage ? alertMessage.title : "Github connected successfully!"}</h2>
          <p className="mt-2">{alertMessage ? alertMessage.content : `You can close this window. Closing automatically in ${closingIn} seconds.`}</p>
        </div>
      )}
    </div>
  );
};

export default Page;
