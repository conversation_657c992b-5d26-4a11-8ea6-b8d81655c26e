// app/organizations/pending-approval/page.tsx
"use client";

import React, { useMemo, useState, useCallback } from "react";
import TableComponent from "@/components/SimpleTable/table";
import { ThreeDotsMenu } from "@/components/UserOnboarding/ui/ThreeDotsMenu";
import { Loading2 } from "@/components/Loaders/Loading";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useRouter } from 'next/navigation';
interface PendingOrganization {
  id: string;
  orgName: string;
  admin: string;
  email: string;
  status: "Approval Pending";
  plan: string;
  requestDate: string;
}

interface FilterState {
  status: string;
  plan: string;
}

const PendingApproval: React.FC = () => {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading] = useState(false);
  const [rowsPerPage] = useState(20);
  const [filters, setFilters] = useState<FilterState>({
    status: "",
    plan: "",
  });
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Mock data
  const data: PendingOrganization[] = [
    {
      id: "1",
      orgName: "TechCorp Solutions",
      admin: "John Smith",
      email: "<EMAIL>",
      status: "Approval Pending",
      plan: "Enterprise",
      requestDate: "Jan 15, 2024",
    },
    {
      id: "2",
      orgName: "HealthCare Plus",
      admin: "Jane Doe",
      email: "<EMAIL>",
      status: "Approval Pending",
      plan: "Enterprise",
      requestDate: "Jan 15, 2024",
    },
  ];



  const handleRowClick = (id: string) => {
    const organization = filteredData.find(org => org.id === id);
    if (organization) {
      router.push(`/dashboard/organizations/pending-approval/${encodeURIComponent(`orgname=${organization.orgName}`)}?orgname="${encodeURIComponent(organization.orgName)}"`);
    }
  };


  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters);
  };

  const handleFilterClick = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const menuItems = [
    {
      label: "Reinvite",
      onClick: (id: string) => {},
    },
    {
      label: "Move to Draft",
      onClick: (id: string) => {},
    },
    {
      label: "Delete",
      onClick: (id: string) => {},
    },
  ];

  // Handlers
  const handleSuspend = useCallback(async (id: string) => {
    try {
      // API call to suspend organization
    } catch (error) {
      
    }
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      // API call to delete organization
    } catch (error) {
      
    }
  }, []);

  const filteredData = useMemo(() => {
    return data
      .filter((org) => {
        const searchableValues = {
          orgName: org.orgName,
          admin: org.admin,
          email: org.email,
          status: org.status,
          plan: org.plan,
          requestDate: org.requestDate,
        };

        const matchesSearch = Object.values(searchableValues)
          .join(" ")
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

        const matchesStatus = !filters.status || org.status === filters.status;
        const matchesPlan = !filters.plan || org.plan === filters.plan;

        return matchesSearch && matchesStatus && matchesPlan;
      })
      .map((org) => ({
        ...org,
        menuActions: (
          <ThreeDotsMenu
            onSuspend={() => handleSuspend(org.id)}
            onDelete={() => handleDelete(org.id)}
          />
        ),
      }));
  }, [data, searchTerm, filters]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center min-h-[200px]">
          <Loading2 />
        </div>
      );
    }

    if (filteredData.length === 0) {
      if (searchTerm) {
        return (
          <EmptyStateView
            type="noSearchResults"
            onClick={() => {
              setSearchTerm("");
            }}
          />
        );
      }
      if (filters.status || filters.plan) {
        return (
          <EmptyStateView
            type="noFilterResults"
            onClick={() => {
              setFilters({ status: "", plan: "" });
            }}
          />
        );
      }
      return (
        <EmptyStateView
          type="noData"
          onClick={() => {}}
        />
      );
    }

    return (
      <TableComponent
        headers={[
          { key: "orgName", label: "ORG NAME" },
          { key: "admin", label: "ADMIN" },
          { key: "email", label: "EMAIL" },
          { key: "status", label: "STATUS" },
          { key: "plan", label: "PLAN" },
          { key: "requestDate", label: "REQUEST DATE" },
          { key: "menuActions", label: "" },
        ]}
        data={filteredData}
        onRowClick={handleRowClick} 
        sortableColumns={{
          orgName: true,
          admin: true,
          email: true,
          plan: true,
          requestDate: true,
        }}
        itemsPerPage={rowsPerPage}
        title="Pending Approval"
      />
    );
  };

  // Filter Dropdown Component
  const FilterDropdown: React.FC<{
    filters: FilterState;
    setFilters: (filters: FilterState) => void;
    onClose: () => void;
  }> = ({ filters, setFilters, onClose }) => {
    const handleClickOutside = (e: MouseEvent) => {
      if (!(e.target as Element).closest(".filter-dropdown")) {
        onClose();
      }
    };

    React.useEffect(() => {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
      <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown">
        <div className="px-4 py-2">
          <h3 className="typography-body-sm font-weight-medium text-semantic-gray-900">Status</h3>
          <div className="mt-2 space-y-2">
            {["Approval Pending"].map((status) => (
              <label key={status} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.status === status}
                  onChange={() =>
                    setFilters({
                      ...filters,
                      status: status === filters.status ? "" : status,
                    })
                  }
                  className="rounded border-semantic-gray-300"
                />
                <span className="ml-2 typography-body-sm text-semantic-gray-700">{status}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="px-4 py-2 border-t">
          <h3 className="typography-body-sm font-weight-medium text-semantic-gray-900">Plan</h3>
          <div className="mt-2 space-y-2">
            {["Enterprise", "Pro", "Basic"].map((plan) => (
              <label key={plan} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.plan === plan}
                  onChange={() =>
                    setFilters({
                      ...filters,
                      plan: plan === filters.plan ? "" : plan,
                    })
                  }
                  className="rounded border-semantic-gray-300"
                />
                <span className="ml-2 typography-body-sm text-semantic-gray-700">{plan}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
    <div className="mt-20">
      <EmptyStateView type="featureNotImplemented" onClick={() => {}} />
    </div>
    </>
  );
};

export default PendingApproval;
