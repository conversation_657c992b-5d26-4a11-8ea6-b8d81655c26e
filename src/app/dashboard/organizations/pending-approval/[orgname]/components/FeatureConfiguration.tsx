// src/app/dashboard/organizations/pending-approval/[orgname]/components/FeatureConfiguration.tsx
import React, { useState } from "react";
import { Info } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Toggle } from "@/components/UserOnboarding/ui/Toggle";

interface FeatureState {
  userManagement: {
    maxUsers: boolean;
    roleCustomization: boolean;
  };
  integrationHub: {
    apiAccess: boolean;
    github: boolean;
    jira: boolean;
    apiAccessRight: boolean;
    githubRight: boolean;
    jiraRight: boolean;
  };
  analyticsBoard: {
    customReports: boolean;
    exportCapabilities: boolean;
  };
}

interface FeatureOption {
  key: string;
  label: string;
}

interface FeatureSection {
  id: keyof FeatureState;
  title: string;
  description: string;
  options: {
    left: FeatureOption[];
    right: FeatureOption[];
  };
}

const featureSections: FeatureSection[] = [
  {
    id: "userManagement",
    title: "User Management",
    description: "Configure user-related features and permissions",
    options: {
      left: [{ key: "maxUsers", label: "Maximum users" }],
      right: [{ key: "roleCustomization", label: "Role customization" }],
    },
  },
  {
    id: "integrationHub",
    title: "Integration Hub",
    description: "Manage external service integrations",
    options: {
      left: [
        { key: "apiAccess", label: "API access" },
        { key: "github", label: "Github" },
        { key: "jira", label: "Jira" },
      ],
      right: [
        { key: "apiAccessRight", label: "API access" },
        { key: "githubRight", label: "Github" },
        { key: "jiraRight", label: "Jira" },
      ],
    },
  },
  {
    id: "analyticsBoard",
    title: "Analytics Dashboard",
    description: "Configure analytics and reporting features",
    options: {
      left: [{ key: "customReports", label: "Custom reports" }],
      right: [{ key: "exportCapabilities", label: "Export capabilities" }],
    },
  },
];

const defaultFeatures: FeatureState = {
  userManagement: {
    maxUsers: false,
    roleCustomization: false,
  },
  integrationHub: {
    apiAccess: false,
    github: false,
    jira: false,
    apiAccessRight: false,
    githubRight: false,
    jiraRight: false,
  },
  analyticsBoard: {
    customReports: false,
    exportCapabilities: false,
  },
};

interface FeatureConfigurationProps {
  onBack: () => void;
  onNext: (featureConfig: { enableAll: boolean; features: FeatureState }) => void;
}

const FeatureConfiguration: React.FC<FeatureConfigurationProps> = ({
  onBack,
  onNext,
}) => {
  const [enableAll, setEnableAll] = useState(false);
  const [features, setFeatures] = useState<FeatureState>(defaultFeatures);
  const [maxUsers, setMaxUsers] = useState<number>(2);

  const handleEnableAll = (enabled: boolean) => {
    setEnableAll(enabled);
    const updatedFeatures = Object.keys(features).reduce<FeatureState>(
      (acc, sectionKey) => {
        const section = features[sectionKey as keyof FeatureState];
        return {
          ...acc,
          [sectionKey]: Object.keys(section).reduce(
            (secAcc, featureKey) => ({
              ...secAcc,
              [featureKey]: enabled,
            }),
            {}
          ),
        };
      },
      defaultFeatures
    );
    setFeatures(updatedFeatures);
  };

  const handleDisableOptional = (disabled: boolean) => {
    setEnableAll(!disabled);
    const updatedFeatures = Object.keys(features).reduce<FeatureState>(
      (acc, sectionKey) => {
        const section = features[sectionKey as keyof FeatureState];
        return {
          ...acc,
          [sectionKey]: Object.keys(section).reduce(
            (secAcc, featureKey) => ({
              ...secAcc,
              [featureKey]: !disabled,
            }),
            {}
          ),
        };
      },
      defaultFeatures
    );
    setFeatures(updatedFeatures);
  };

  return (
    <div className="w-full max-w-[1200px] max-h-[74vh]">
      <div className="flex flex-col">
        {/* Header */}
        <div className="mb-1">
          <h2 className="project-panel-heading mb-2">Configure Features</h2>
          <div className="flex gap-8 mb-4">
            <div className="flex flex-col space-y-2">
              <span className="typography-body-sm text-semantic-gray-600">
                Enable all available features
              </span>
              <div className="flex justify-start">
                <Toggle
                  enabled={enableAll}
                  onChange={handleEnableAll}
                  size="small"
                />
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="typography-body-sm text-semantic-gray-600">
                Disable optional features
              </span>
              <div className="flex justify-start">
                <Toggle
                  enabled={!enableAll}
                  onChange={handleDisableOptional}
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Feature Sections */}
        <div className="flex-1 overflow-y-auto space-y-4">
          {featureSections.map((section) => (
            <div
              key={section.id}
              className="bg-white rounded-lg border border-semantic-gray-200 p-3"
            >
              <div className="flex items-center justify-between mb-1 bg-primary-50 rounded-md p-1.5">
                <div className="flex items-center gap-2">
                  <h3 className="text-[#1C64F2] font-weight-semibold">
                    {section.title}
                  </h3>
                  <div className="relative group">
                    <Info
                      className="w-4 h-4 text-semantic-gray-400 cursor-help"
                      aria-label={section.description}
                    />
                  </div>
                </div>
                <Toggle
                  enabled={Object.values(features[section.id]).every(Boolean)}
                  onChange={(enabled) => {
                    setFeatures((prev) => ({
                      ...prev,
                      [section.id]: Object.fromEntries(
                        Object.keys(prev[section.id]).map((key) => [
                          key,
                          enabled,
                        ])
                      ),
                    }));
                  }}
                  size="small"
                />
              </div>

              <div className="grid grid-cols-2 gap-8">
                {/* Left Column */}
                <div className="space-y-3">
                  {section.options.left.map((option) => (
                    <div
                      key={`${section.id}-${option.key}`}
                      className="flex flex-col space-y-1"
                    >
                      <span className="typography-body-sm text-semantic-gray-600">
                        {option.label}
                      </span>
                      <div className="flex justify-start">
                        {option.key === "maxUsers" ? (
                          <input
                            type="number"
                            className="w-full h-9 px-3 border border-semantic-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                            value={maxUsers}
                            onChange={(e) =>
                              setMaxUsers(Number(e.target.value))
                            }
                            min={1}
                          />
                        ) : (
                          <Toggle
                            enabled={
                              features[section.id][
                                option.key as keyof (typeof features)[typeof section.id]
                              ] || false
                            }
                            onChange={(enabled) => {
                              setFeatures((prev) => ({
                                ...prev,
                                [section.id]: {
                                  ...prev[section.id],
                                  [option.key]: enabled,
                                },
                              }));
                            }}
                            size="small"
                          />
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Right Column */}
                <div className="space-y-3">
                  {section.options.right.map((option) => (
                    <div
                      key={`${section.id}-${option.key}`}
                      className="flex flex-col space-y-1"
                    >
                      <span className="typography-body-sm text-semantic-gray-600">
                        {option.label}
                      </span>
                      <div className="flex justify-start">
                        <Toggle
                          enabled={
                            features[section.id][
                              option.key as keyof (typeof features)[typeof section.id]
                            ] || false
                          }
                          onChange={(enabled) => {
                            setFeatures((prev) => ({
                              ...prev,
                              [section.id]: {
                                ...prev[section.id],
                                [option.key]: enabled,
                              },
                            }));
                          }}
                          size="small"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Navigation */}
        <div className="flex justify-between py-4 mt-2">
          <DynamicButton variant="secondary" text="Back" onClick={onBack} />
          <DynamicButton
            variant="primary"
            text="Next : Review & Confirm"
            onClick={() => onNext({ enableAll, features })}
          />
        </div>
      </div>
    </div>
  );
};

export default FeatureConfiguration;