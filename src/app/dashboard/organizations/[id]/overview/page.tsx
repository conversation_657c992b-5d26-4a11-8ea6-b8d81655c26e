// src/app/dashboard/organizations/[id]/overview/page.tsx
"use client";

import { FC, useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import OrganizationDetails from '@/components/UserOnboarding/OrganizationDetails/OrganizationDetails';
import { fetchOrganization } from '@/utils/api';
import Loader from "@/components/UserOnboarding/ui/Loader"

interface Organization {
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  configurations: {
    max_users: number;
  };
  created_at: string;
  id: string;
}

const OverviewPage: FC = () => {
  const params = useParams();
  const [organizationData, setOrganizationData] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const orgId = params.id as string;
        const data = await fetchOrganization(orgId);
        setOrganizationData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch organization data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  if (isLoading) {
    return (
      <Loader type="groupDetails" />
    );
  }

  if (error) {
    return (
      <div className="text-red-600 text-center p-4">
        Error loading organization data: {error}
      </div>
    );
  }

  if (!organizationData) {
    return (
      <div className="text-semantic-gray-600 text-center p-4">
        No organization data found
      </div>
    );
  }

  // Extract numeric value from company size (e.g., "200+ employees" -> 200)
  const userCount = organizationData.company_size 
    ? parseInt(organizationData.company_size.split('+')[0]) 
    : 0;

  return (
    <div className="space-y-6">
      {/* <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <StatsCard
          title="Users"
          value={userCount}
          total={organizationData.configurations.max_users}
          subtitle="available"
          progress={(userCount / organizationData.configurations.max_users) * 100}
        />
        <StatsCard
          title="Storage"
          value={'0'}
          total={'0'}
          subtitle="available"
          progress={0}
        />
        <StatsCard
          title="API Usage"
          value={'0'}
          total={'0'}
          subtitle="available"
          progress={0}
        />
      </div>
       */}
      <OrganizationDetails
        name={organizationData.name}
        industryType={organizationData.industrial_type}
        companySize={organizationData.company_size}
        businessEmail={organizationData.business_email}
      />
    </div>
  );
};

export default OverviewPage;