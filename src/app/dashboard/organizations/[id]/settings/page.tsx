'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Toggle } from '@/components/UserOnboarding/ui/Toggle';
import { fetchOrganization, updateOrganization } from '@/utils/api';
import { OrganizationState } from '@/components/Interfaces/Dashboard/Organization';
import { useParams } from 'next/dist/client/components/navigation';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';

interface SettingsSection {
  title: string;
  description: string;
  settings: SettingItem[];
}

interface SettingItem {
  key: string;
  label: string;
  description?: string;
  type: 'toggle' | 'checkbox' | 'text' | 'integer' | 'dropdown';
  defaultValue: boolean | string | number;
  value: boolean | string | number;
  options?: string[];
  min?: number;
  max?: number;
}

interface ModuleSettings {
  name: string;
  display_name: string;
  selected_model: string;
  available_models: string[];
}

interface Settings {
  enable_log_download_pod_crud: boolean;
  showfunctioncalling: boolean;
  discussion: ModuleSettings;
  code_generation: ModuleSettings;
  code_inspection: ModuleSettings;
  conversational_chat: ModuleSettings;
}

type ModuleSettingKey = Exclude<keyof Settings, 'showfunctioncalling'| 'enable_log_download_pod_crud'>;

const SettingsPage = () => {
  const { id } = useParams();
  const { showAlert } = useContext(AlertContext);
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<Settings>({
    showfunctioncalling: false,
    enable_log_download_pod_crud: false,
    discussion: {
      name: 'discussion',
      display_name: 'Discussion Framework',
      selected_model: 'claude-3.5-sonnet',
      available_models: ['gpt-4o-mini', 'gpt-4o', 'gpt-4-turbo', 'gpt-3.5']
    },
    code_generation: {
      name: 'code_generation',
      display_name: 'Code Generation',
      selected_model: 'claude-3.5-sonnet',
      available_models: ['gpt-4o-mini', 'gpt-4o', 'claude-3.5-sonnet']
    },
    code_inspection: {
      name: 'code_inspection',
      display_name: 'Code Inspection',
      selected_model: 'claude-3.5-sonnet',
      available_models: ['gpt-4o-mini', 'gpt-4o', 'gpt-4-turbo', 'claude-3.5-sonnet']
    },
    conversational_chat: {
      name: 'conversational_chat',
      display_name: 'Conversational Chat',
      selected_model: 'claude-3.5-sonnet',
      available_models: ['gpt-4o-mini', 'gpt-4o', 'gpt-4-turbo', 'claude-3.5-sonnet']
    }
  });
  const [organization, setOrganization] = useState<OrganizationState>();
  const [freeCredits, setFreeCredits] = useState(1000);

  useEffect(() => {
    fetchOrganization(id).then((organization) => {
      setOrganization(organization);
      setFreeCredits(organization?.credits ?? 1000);
      if (organization?.settings) {
        setSettings(prevSettings => ({
          ...prevSettings,
          showfunctioncalling: organization.settings.showfunctioncalling ?? false,
          enable_log_download_pod_crud: organization.settings.enable_log_download_pod_crud ?? false,

          discussion: {
            ...prevSettings.discussion,
            ...organization.settings.discussion
          },
          code_generation: {
            ...prevSettings.code_generation,
            ...organization.settings.code_generation
          },
          code_inspection: {
            ...prevSettings.code_inspection,
            ...organization.settings.code_inspection
          },
          conversational_chat: {
            ...prevSettings.conversational_chat,
            ...organization.settings.conversational_chat
          }
        }));
      }
    });
  }, [id]);

  const handleFreeCreditsChange = (value: number) => {
    setFreeCredits(value);
  };

  const handleModuleSettingChange = (moduleKey: ModuleSettingKey, value: string) => {
    setSettings(prev => ({
      ...prev,
      [moduleKey]: {
        ...prev[moduleKey],
        selected_model: value
      }
    }));
  };

  const handleToggleChange = (key: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = () => {
    if (!organization) return;

    const updatedSettings = {
      ...settings
    };

    setLoading(true);
    const updatedOrganization = {
      ...organization,
      settings: updatedSettings,
      credits: freeCredits
    };

    updateOrganization(updatedOrganization)
      .then(() => {
        showAlert('Settings saved successfully', 'success');
      })
      .catch((error) => {
        showAlert("Failed to save settings", 'error');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const sections: SettingsSection[] = [
    {
      title: 'General Settings',
      description: 'Configure general application settings',
      settings: [
        {
          key: 'showfunctioncalling',
          label: 'Show Function Calling',
          description: 'Display function calling details in the UI',
          type: 'toggle',
          defaultValue: settings.showfunctioncalling,
          value: settings.showfunctioncalling
        }
      ]
    },
    {
      title: 'Logs Download and Pods Crud Setting',
      description: 'Configure Logs Download and pod crud setting',
      settings: [
        {
          key: 'enable_log_download_pod_crud',
          label: 'Allow logs download and Pods crud ',
          description: 'Allow to download logs and pods crud',
          type: 'toggle',
          defaultValue: settings.enable_log_download_pod_crud,
          value: settings.enable_log_download_pod_crud
        }
      ]
    },
    {
      title: 'Credits Management',
      description: 'Manage organization credits',
      settings: [
        {
          key: 'credits',
          label: 'Credits',
          description: 'Available credits for the organization',
          type: 'integer',
          defaultValue: freeCredits,
          value: freeCredits,
          min: 0,
          max: 99999999
        }
      ]
    },
    {
      title: 'AI Models Configuration',
      description: 'Configure AI model settings for different modules',
      settings: [
        {
          key: 'discussion',
          label: 'Discussion Framework',
          description: 'Select AI model for discussion framework',
          type: 'dropdown',
          defaultValue: settings.discussion.selected_model,
          value: settings.discussion.selected_model,
          options: settings.discussion.available_models
        },
        {
          key: 'code_generation',
          label: 'Code Generation',
          description: 'Select AI model for code generation',
          type: 'dropdown',
          defaultValue: settings.code_generation.selected_model,
          value: settings.code_generation.selected_model,
          options: settings.code_generation.available_models
        },
        {
          key: 'code_inspection',
          label: 'Code Inspection',
          description: 'Select AI model for code inspection',
          type: 'dropdown',
          defaultValue: settings.code_inspection.selected_model,
          value: settings.code_inspection.selected_model,
          options: settings.code_inspection.available_models
        },
        {
          key: 'conversational_chat',
          label: 'Conversational Chat',
          description: 'Select AI model for chat',
          type: 'dropdown',
          defaultValue: settings.conversational_chat.selected_model,
          value: settings.conversational_chat.selected_model,
          options: settings.conversational_chat.available_models
        }
      ]
    }
  ];

  const renderSettingInput = (setting: SettingItem) => {
    switch (setting.type) {
      case 'integer':
      return (
        <div className="flex items-center">
          <input
            type="text" // Changed from "number" to "text"
            value={setting.value as number}
            onChange={(e) => {
              // Only allow numbers
              const value = e.target.value.replace(/[^0-9]/g, '');
              // Convert to number and handle the change
              const numValue = value === '' ? 0 : parseInt(value);
              // Ensure value is within bounds
              if (numValue >= (setting.min || 0) && numValue <= (setting.max || 100000)) {
                handleFreeCreditsChange(numValue);
              }
            }}
            className="block w-full rounded-md border-semantic-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:typography-body-sm"
            style={{
              // Remove spinner arrows
              WebkitAppearance: 'none',
              MozAppearance: 'textfield'
            }}
          />
          <span className="ml-2 typography-body-sm text-semantic-gray-500">credits</span>
        </div>
      );


      case 'toggle':
        return (
          <Toggle
            enabled={setting.value as boolean}
            onChange={(value) => handleToggleChange(setting.key, value)}
            size="small"
          />
        );

      case 'dropdown':
        return (
          <select
            value={setting.value as string}
            onChange={(e) => handleModuleSettingChange(setting.key as ModuleSettingKey, e.target.value)}
            className="block w-full rounded-md border-semantic-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:typography-body-sm"
          >
            {setting.options?.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-[1200px]">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="typography-heading-4 font-weight-semibold">Settings</h1>
          <p className="text-semantic-gray-600 mt-1">Manage your settings and preferences</p>
        </div>
        
        <DynamicButton
          text="Save Settings"
          onClick={handleSaveSettings}
          loading={loading}
        />
      </div>

      <div className="space-y-6 overflow-auto">
        {sections.map((section) => (
          <div key={section.title} className="bg-white rounded-lg border border-semantic-gray-200 p-6">
            <div className="mb-6">
              <h2 className="text-primary font-weight-medium">{section.title}</h2>
              <p className="typography-body-sm text-semantic-gray-600 mt-1">{section.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-8">
              {section.settings.map((setting) => (
                <div key={setting.key} className="flex flex-col space-y-2">
                  <label className="typography-body-sm font-weight-medium text-semantic-gray-700">
                    {setting.label}
                  </label>
                  {setting.description && (
                    <p className="typography-caption text-semantic-gray-500">{setting.description}</p>
                  )}
                  {renderSettingInput(setting)}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SettingsPage;