
//@ts-nocheck
'use client'
import { useState, useEffect, useContext } from 'react';
import { Save, Send, Eye, Edit2 } from 'lucide-react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { createDraft, getDrafts, deleteDraft, publishAnnouncement, getAllAnnouncements, deleteAnnouncement } from '@/utils/announcementAPI';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { DraftsList } from './components/DraftsList';
import { LivePreview } from './components/LivePreview';
import { AnnouncementsList } from './components/AnnouncementList';
import { TabSelector } from './components/TabSelector';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs, { Dayjs } from 'dayjs';
import timezone from 'dayjs/plugin/timezone';

import utc from 'dayjs/plugin/utc';
// If you need TextField

dayjs.extend(utc);
dayjs.extend(timezone);


const ReactQuill = dynamic(() => import('react-quill'), { ssr: false });

interface AnnouncementDraft {
  id: string;
  content: string;
  detailed_content?: string;
  type: 'alert' | 'maintenance' | 'announcement';
  created_at: string;
  last_saved: string;
  user_id: string;
  tenant_id: string;
}

interface Announcement {
  id: string;
  content: string;
  detailed_content?: string;
  type: 'alert' | 'maintenance' | 'announcement';
  is_active: boolean;
  published_at: string;
  expires_at?: string;
  created_at: string;
}

export default function AnnouncementsPage() {
  // Basic announcement states
  const [content, setContent] = useState('');
  const [detailedContent, setDetailedContent] = useState('');
  const [type, setType] = useState<'alert' | 'maintenance' | 'announcement'>('announcement');
  const [drafts, setDrafts] = useState<AnnouncementDraft[]>([]);
  const [previewMode, setPreviewMode] = useState(false);
  const [deletingDraftId, setDeletingDraftId] = useState<string | null>(null);
  const [savingDraft, setSavingDraft] = useState(false);
  const [publishingAnnouncement, setPublishingAnnouncement] = useState(false);

  const [activeView, setActiveView] = useState<'drafts' | 'announcements'>('drafts');

  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loadingAnnouncements, setLoadingAnnouncements] = useState(false);
  const [deletingAnnouncementId, setDeletingAnnouncementId] = useState<string | null>(null);

  // Scheduling states
  const [publishAt, setPublishAt] = useState<Date | null>(null);
  const [expiresAt, setExpiresAt] = useState<Date | null>(null);
  const [schedulePublish, setSchedulePublish] = useState(false);
  const [hasExpiry, setHasExpiry] = useState(false);

  // Loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const {showAlert} = useContext(AlertContext);

  useEffect(() => {
    fetchDrafts();
  }, [activeView]);

  const fetchDrafts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedDrafts = await getDrafts();
      setDrafts(fetchedDrafts);
    } catch (error) {
      
      setError('Failed to load drafts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  
  const handleDateChange = (date: Dayjs | null, setDate: (date: Date | null) => void) => {
    if (date) {
      const utcDate = date.utc().toDate();
      setDate(utcDate);
    } else {
      setDate(null);
    }
  };

  const formatDateToUTC = (date: Date | null): string => {
    if (!date) return '';
    return dayjs(date).utc().format('YYYY-MM-DD hh:mm A [UTC]');
  };

  const fetchAnnouncements = async () => {
    setLoadingAnnouncements(true);
    try {
      const data = await getAllAnnouncements();
      setAnnouncements(data);
    } catch (error) {
      
      showAlert('Failed to load announcements', 'error');
    } finally {
      setLoadingAnnouncements(false);
    }
  };
  
  useEffect(() => {
    fetchAnnouncements();
  }, [activeView]);
  
  const handleDeleteAnnouncement = async (id: string) => {
    setDeletingAnnouncementId(id);
    try {
      await deleteAnnouncement(id);
      setAnnouncements(prev => prev.filter(a => a.id !== id));
      showAlert('Announcement deleted successfully', 'success');
    } catch (error) {
      
      showAlert('Failed to delete announcement', 'error');
    } finally {
      setDeletingAnnouncementId(null);
    }
  };

  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link', 'code-block'],
      ['clean']
    ]
  };

  const quillFormats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet',
    'link', 'code-block'
  ];

  const handleSaveDraft = async () => {
    setSavingDraft(true);
    try {
      const draftData = {
        content,
        detailed_content: detailedContent,
        type,
        published_at: schedulePublish ? publishAt : null,
        expires_at: hasExpiry ? expiresAt : null
      };
  
      const newDraft = await createDraft(draftData);
      setDrafts(prevDrafts => [...prevDrafts, newDraft]);
      showAlert('Draft saved successfully', 'success');
    } catch (error) {
      
      showAlert('Failed to save draft. Please try again.', 'error');
    } finally {
      setSavingDraft(false);
    }
  };
  
  const handleDeleteDraft = async (draftId: string) => {
    setDeletingDraftId(draftId);
    try {
      await deleteDraft(draftId);
      setDrafts(prevDrafts => prevDrafts.filter(draft => draft.id !== draftId));
      showAlert('Draft deleted successfully', 'success');
    } catch (error) {
      
      showAlert('Failed to delete draft. Please try again.', 'error');
    } finally {
      setDeletingDraftId(null);
    }
  };

  const loadDraft = (draft: AnnouncementDraft) => {
    setContent(draft.content);
    setDetailedContent(draft.detailed_content || '');
    setType(draft.type);
  };

  const handlePublishAnnouncement = async () => {
    if (!validateAnnouncement()) {
      return;
    }
    setPublishingAnnouncement(true);
    try {
      const announcementData = {
        content,
        detailed_content: detailedContent,
        type,
        published_at: schedulePublish ? publishAt : new Date(),
        expires_at: hasExpiry ? expiresAt : null
      };
  
      await publishAnnouncement(announcementData);
      
      setContent('');
      setDetailedContent('');
      setType('announcement');
      setPublishAt(null);
      setExpiresAt(null);
      setSchedulePublish(false);
      setHasExpiry(false);
  
      showAlert('Announcement published successfully', 'success');
    } catch (error) {
      
      showAlert('Failed to publish announcement. Please try again.', 'error');
    } finally {
      setPublishingAnnouncement(false);
    }
  };

  const validateAnnouncement = () => {
    if (!content.trim()) {
      showAlert('Please enter a short message', 'error');
      return false;
    }
  
    if (schedulePublish && !publishAt) {
      showAlert('Please select a publish date', 'error');
      return false;
    }
  
    if (hasExpiry && !expiresAt) {
      showAlert('Please select an expiry date', 'error');
      return false;
    }
  
    if (hasExpiry && publishAt && expiresAt && expiresAt <= publishAt) {
      showAlert('Expiry date must be after publish date', 'error');
      return false;
    }
  
    return true;
  };

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md">
          {error}
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="typography-heading-2 font-weight-bold">Manage Announcements</h1>
        <button
          onClick={() => setPreviewMode(!previewMode)}
          className="flex items-center gap-2 px-4 py-2 bg-white border rounded-md hover:bg-semantic-gray-50"
        >
          {previewMode ? (
            <>
              <Edit2 className="w-4 h-4" />
              Edit Mode
            </>
          ) : (
            <>
              <Eye className="w-4 h-4" />
              Preview Mode
            </>
          )}
        </button>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div className={`space-y-4 pb-12 mb-4 ${previewMode ? 'hidden' : ''}`}>
          <div className="p-6 mb-6 bg-white rounded-lg shadow-sm border">
            <div className="mb-4">
              <label className="block typography-body-sm font-weight-medium text-semantic-gray-700 mb-2">
                Announcement Type
              </label>
              <select
                value={type}
                onChange={(e) => setType(e.target.value as any)}
                className="w-full p-2 border rounded-md bg-white"
              >
                <option value="announcement">📢 Announcement</option>
                <option value="maintenance">🔧 Maintenance</option>
                <option value="alert">⚠️ Alert</option>
              </select>
            </div>

            <div className="mb-4">
              <label className="block typography-body-sm font-weight-medium text-semantic-gray-700 mb-2">
                Short Message
              </label>
              <input
                type="text"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full p-2 border rounded-md"
                placeholder="Enter brief announcement message..."
              />
            </div>

            <div className='flex flex-col pb-12 h-100'>
              <label className="block typography-body-sm font-weight-medium text-semantic-gray-700 mb-2">
                Detailed Message
              </label>
              <div className="rounded-md h-64">
                <ReactQuill
                  value={detailedContent}
                  onChange={setDetailedContent}
                  modules={quillModules}
                  formats={quillFormats}
                  className="h-64"
                  theme="snow"
                />
              </div>
            </div>

            <div className="space-y-4 mt-6 border-t pt-6">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="schedulePublish"
                  checked={schedulePublish}
                  onChange={(e) => setSchedulePublish(e.target.checked)}
                  className="rounded border-semantic-gray-300"
                />
                <label htmlFor="schedulePublish" className="typography-body-sm font-weight-medium text-semantic-gray-700">
                  Schedule Publication
                </label>
              </div>

              {schedulePublish && (
              <div className="ml-6">
                <LocalizationProvider dateAdapter={AdapterDayjs}>
                  <DateTimePicker
                    label="Publish At (UTC)"
                    value={publishAt}
                    onChange={(newValue: Dayjs | null) => {
                      setPublishAt(newValue ? dayjs.utc(newValue) : null);
                    }}
                    timezone="UTC"
                    ampm={true}
                    slotProps={{
                      textField: {
                        helperText: 'Time is in UTC'
                      }
                    }}
                  />
                </LocalizationProvider>
                {publishAt && (
                  <p className="typography-body-sm text-semantic-gray-500 mt-1">
                    Selected time: {publishAt.format('YYYY-MM-DD hh:mm A [UTC]')}
                  </p>
                )}
              </div>
            )}
              <div className="flex items-center gap-2 mt-4">
                <input
                  type="checkbox"
                  id="hasExpiry"
                  checked={hasExpiry}
                  onChange={(e) => setHasExpiry(e.target.checked)}
                  className="rounded border-semantic-gray-300"
                />
                <label htmlFor="hasExpiry" className="typography-body-sm font-weight-medium text-semantic-gray-700">
                  Set Expiration Date
                </label>
              </div>

              {hasExpiry && (
                <div className="ml-6">
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DateTimePicker
                      label="Expires At (UTC)"
                      value={expiresAt}
                      onChange={(newValue: Dayjs | null) => {
                        setExpiresAt(newValue ? dayjs.utc(newValue) : null);
                      }}
                      timezone="UTC"
                      ampm={true}
                      slotProps={{
                        textField: {
                          helperText: 'Time is in UTC'
                        }
                      }}
                    />
                  </LocalizationProvider>
                  {expiresAt && (
                    <p className="typography-body-sm text-semantic-gray-500 mt-1">
                      Selected time: {expiresAt.format('YYYY-MM-DD hh:mm A [UTC]')}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={handleSaveDraft}
              disabled={savingDraft}
              className="flex items-center px-4 py-2 bg-white border rounded-md hover:bg-semantic-gray-50 disabled:opacity-50"
            >
              <Save className="w-4 h-4 mr-2" />
              {savingDraft ? 'Saving...' : 'Save Draft'}
            </button>
            <button
              onClick={handlePublishAnnouncement}
              disabled={publishingAnnouncement || !content}
              className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
            >
              <Send className="w-4 h-4 mr-2" />
              {publishingAnnouncement ? 'Publishing...' : 'Publish'}
            </button>
          </div>
        </div>

        <LivePreview 
          previewMode={previewMode}
          type={type}
          content={content}
          detailedContent={detailedContent}
          schedulePublish={schedulePublish}
          hasExpiry={hasExpiry}
          publishAt={publishAt}
          expiresAt={expiresAt}
        />
      </div>

      <div className="mt-8">
        <TabSelector activeView={activeView} onViewChange={setActiveView} />
        
        {activeView === 'drafts' ? (
          <DraftsList 
            drafts={drafts}
            isLoading={isLoading}
            deletingDraftId={deletingDraftId}
            onLoadDraft={loadDraft}
            onDeleteDraft={handleDeleteDraft}
          />
        ) : (
          <AnnouncementsList
            announcements={announcements}
            isLoading={loadingAnnouncements}
            deletingId={deletingAnnouncementId}
            onDelete={handleDeleteAnnouncement}
          />
        )}
      </div>
    </div>
  );
}