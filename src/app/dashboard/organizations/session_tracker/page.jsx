// pages/dashboard.jsx

"use client"
import { useEffect, useState, useMemo } from 'react';
import { useDashboard, VIEWS } from '@/hooks/useDashboard';
import DashboardHeader from '@/components/dashboard/DashboardHeader';
import StatsCards from '@/components/dashboard/StatsCards';
import Breadcrumb from '@/components/dashboard/Breadcrumb';
import LoadingOverlay from '@/components/dashboard/LoadingOverlay';
import SessionChart from '@/components/dashboard/SessionChart';
import TenantsList from '@/components/dashboard/TenantsList';
import UsersList from '@/components/dashboard/UsersList';
import SessionsList from '@/components/dashboard/SessionsList';
import SessionDetails from '@/components/dashboard/SessionDetails';
import { getOverallStats, getChartData, getTenantsValue, getUsersByTenant } from '@/utils/api';

const Page = () => {
  const {
    // State
    currentView,
    loading,
    error,
    overallStats,
    tenants,
    users,
    sessions,
    sessionDetails,
    selectedTenant,
    selectedUser,
    dateFrom,
    dateTo,
    // Actions
    setDateFrom,
    setDateTo,
    navigateToTenant,
    navigateToUser,
    navigateToSession,
    navigateToOverview,
    navigateToTenantView,
    navigateToUserView,
    refreshData,
    filterSessions,
    clearError,
    // Initial load
    loadOverallStats,
    loadTenants,
  } = useDashboard();

  const defaultDates = useMemo(() => {
    const now = new Date();

    // From Date: Current date at 6:00 AM
    const fromDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 6, 0, 0);

    // To Date: Current date and time
    const toDate = new Date(now);

    // Format for datetime-local input using local time (YYYY-MM-DDTHH:mm)
    const formatDateTime = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    };

    return {
      from: formatDateTime(fromDate), // Format for datetime-local
      to: formatDateTime(toDate)
    };
  }, []);

  // Local state for filters and dashboard data
  const [tenantNavigationLoading, setTenantNavigationLoading] = useState(false);

  const [lastUpdated, setLastUpdated] = useState(null);
  const [filterLoading, setFilterLoading] = useState(false);
  const [filteredStats, setFilteredStats] = useState(null);
  const [filteredTenants, setFilteredTenants] = useState(null);
  const [filters, setFilters] = useState({
    dateFrom: defaultDates.from,
    dateTo: defaultDates.to,
    status: 'all',
    service: "all"
  });
  const [chartData, setChartData] = useState([]);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState(null);
  const [granularity, setGranularity] = useState('day');

  // Initialize dashboard data
  useEffect(() => {
    const initDashboard = async () => {
      try {
        // Load the original data for the hook
        await Promise.all([
          loadOverallStats(),
          loadTenants()
        ]);

        // Apply the default "clear" state
        await clearFilters();

      } catch (error) {
        console.error('Failed to initialize dashboard:', error);
        setChartError({ message: 'Failed to initialize dashboard' });
      }
    };

    initDashboard();
  }, [loadOverallStats, loadTenants]);
  const applyFilters = async (filterOverrides = {}, granularityOverride = null) => {
    try {
      setFilterLoading(true);
      setChartLoading(true);
      setChartError(null);

      const currentFilters = { ...filters, ...filterOverrides };
      const currentGranularity = granularityOverride || granularity;
      const { dateFrom, dateTo, status, service } = currentFilters;
      const statusFilter = status === 'all' ? null : status;
      const serviceFilter = service === 'all' ? null : service;

      // Convert datetime-local format to ISO string
      const fromDate = dateFrom ? new Date(dateFrom).toISOString() : null;
      const toDate = dateTo ? new Date(dateTo).toISOString() : null;

      // Fetch filtered data concurrently
      const [statsData, tenantsData, chartDataResult] = await Promise.all([
        getOverallStats(fromDate, toDate, statusFilter), // Stats respect status filter
        getTenantsValue(fromDate, toDate, statusFilter, serviceFilter), // Tenants respect status filter
        getChartData(fromDate, toDate, null, currentGranularity), // Chart IGNORES status filter (always null)
      ]);

      // Update filtered state
      setFilteredStats(statsData);

      // Handle tenants data
      if (tenantsData && tenantsData.tenants) {
        setFilteredTenants(tenantsData.tenants);
      } else if (Array.isArray(tenantsData)) {
        setFilteredTenants(tenantsData);
      } else {
        console.warn('Unexpected tenants data structure:', tenantsData);
        setFilteredTenants([]);
      }

      // Handle chart data
      if (chartDataResult && chartDataResult.chart_data) {
        setChartData(chartDataResult.chart_data);
      } else if (Array.isArray(chartDataResult)) {
        setChartData(chartDataResult);
      } else {
        setChartData([]);
      }

      setLastUpdated(new Date().toLocaleTimeString());

    } catch (error) {
      console.error('Failed to apply filters:', error);
      setChartError({ message: 'Failed to load filtered data' });
    } finally {
      setFilterLoading(false);
      setChartLoading(false);
    }
  };

  // Clear filters - Shows ALL sessions from beginning up to now
  const clearFilters = async () => {
    const resetFilters = {
      dateFrom: defaultDates.from,
      dateTo: defaultDates.to,
      status: 'all',
      service: 'all'
    };

    try {
      setFilterLoading(true);
      setChartLoading(true);
      setChartError(null);

      // Update filters and granularity state
      setFilters(resetFilters);
      setGranularity('day');

      // Load data with correct date ranges
      const fromDate = new Date(resetFilters.dateFrom).toISOString();
      const toDate = new Date(resetFilters.dateTo).toISOString();

      const [statsData, tenantsData, chartDataResult] = await Promise.all([
        // Stats: 6 AM today to now, all statuses
        getOverallStats(fromDate, toDate, null),
        // Tenants: 6 AM today to now, all statuses, all services
        getTenantsValue(fromDate, toDate, null, null),
        // Chart: All sessions from beginning of time
        getChartData(null, null, null, 'day')
      ]);

      // Update filtered state
      setFilteredStats(statsData);

      // Handle tenants data
      if (tenantsData && tenantsData.tenants) {
        setFilteredTenants(tenantsData.tenants);
      } else if (Array.isArray(tenantsData)) {
        setFilteredTenants(tenantsData);
      } else {
        setFilteredTenants([]);
      }

      // Handle chart data
      setChartData(chartDataResult.chart_data || []);
      setLastUpdated(new Date().toLocaleTimeString());

    } catch (error) {
      console.error('Failed to clear filters:', error);
      setChartError({ message: 'Failed to clear filters' });
    } finally {
      setFilterLoading(false);
      setChartLoading(false);
    }
  };


  // Load chart data when granularity changes
  useEffect(() => {
    const loadChartData = async () => {
      try {
        setChartLoading(true);
        const { dateFrom, dateTo } = filters; // Get dates but ignore status
        const fromDate = dateFrom ? new Date(dateFrom).toISOString() : null;
        const toDate = dateTo ? new Date(dateTo).toISOString() : null;

        // Chart always ignores status filter
        const chartDataResult = await getChartData(fromDate, toDate, null, granularity);
        setChartData(chartDataResult.chart_data || []);
      } catch (error) {
        console.error('Failed to load chart data:', error);
        setChartError({ message: 'Failed to load chart data' });
      } finally {
        setChartLoading(false);
      }
    };

    // Only reload chart data when granularity changes (if filters are applied)
    if (filters.dateFrom || filters.dateTo || filters.status !== 'all') {
      loadChartData();
    }
  }, [granularity]);

  // Handle refresh
  const handleRefresh = async () => {
    try {
      if (filters.dateFrom || filters.dateTo || filters.status !== 'all') {
        // If filters are applied, refresh with filters
        await applyFilters();
      } else {
        // Otherwise, refresh all data
        await refreshData();
        setLastUpdated(new Date().toLocaleTimeString());

        // Also refresh chart data
        setChartLoading(true);
        const chartDataResult = await getChartData(null, null, null, granularity);
        setChartData(chartDataResult.chart_data || []);
        setChartLoading(false);
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
      setChartLoading(false);
    }
  };

  // Handle tenant selection from dropdown
  const handleTenantDropdownChange = (tenantId) => {
    if (tenantId) {
      // Use filtered tenants if available, otherwise use original tenants
      const tenantsToSearch = filteredTenants || tenants;
      const tenant = tenantsToSearch.find(t => t.id === tenantId);
      if (tenant) {
        const currentFilters = {
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          status: filters.status === 'all' ? null : filters.status,
          service: filters.service === 'all' ? null : filters.service
        };
        navigateToTenant(tenant, currentFilters);
      }
    }
  };

  const handleClick = async (tenant) => {
    const currentFilters = {
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo,
      status: filters.status === 'all' ? null : filters.status,
      service: filters.service === 'all' ? null : filters.service
    };

    try {
      setTenantNavigationLoading(true);

      // Convert filters for API call - create the filters object that matches your API
      const apiFilters = {};
      if (currentFilters.dateFrom) {
        apiFilters.dateFrom = new Date(currentFilters.dateFrom).toISOString();
      }
      if (currentFilters.dateTo) {
        apiFilters.dateTo = new Date(currentFilters.dateTo).toISOString();
      }
      if (currentFilters.status) {
        apiFilters.status = currentFilters.status;
      }
      if (currentFilters.service) {
        apiFilters.service = currentFilters.service;
      }

      // Load fresh user data with current filters using the correct API signature
      const usersData = await getUsersByTenant(tenant.id, apiFilters);


      // Navigate with the current filters and the loaded data
      navigateToTenant(tenant, currentFilters, usersData);

    } catch (error) {
      console.error('Failed to load tenant users:', error);
      // Handle error appropriately - you might want to show a toast or error message
    } finally {
      setTenantNavigationLoading(false);
    }
  }


  const handleUsersListClick = (users) => {
    const currentFilters = {
      dateFrom: filters.dateFrom,
      dateTo: filters.dateTo,
      status: filters.status === 'all' ? null : filters.status,
      service: filters.service === 'all' ? null : filters.service
    };
    navigateToUser(users, currentFilters);
  }

  // Error handling
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Handle filter input changes
  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };


  // Check if filters are applied
  const hasFiltersApplied =
    filters.dateFrom !== defaultDates.from ||
    filters.dateTo !== defaultDates.to ||
    filters.status !== 'all' ||
    filters.service !== 'all';

  // Determine which data to display
  const currentStats = filteredStats || overallStats;
  const currentTenants = filteredTenants || tenants;

  return (
    <div className="bg-semantic-gray-50 min-h-screen">
      {/* Header */}
      <DashboardHeader
        onRefresh={handleRefresh}
        loading={loading || filterLoading}
        lastUpdated={lastUpdated}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters Section - Only show on Overview */}
        {currentView === VIEWS.OVERVIEW && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div>
                <label className="block text-sm font-medium text-semantic-gray-700 mb-2">
                  From Date & Time
                </label>
                <input
                  type="datetime-local"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="w-full border border-semantic-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="dd/mm/yyyy, --:--"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-semantic-gray-700 mb-2">
                  To Date & Time
                </label>
                <input
                  type="datetime-local"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="w-full border border-semantic-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="dd/mm/yyyy, --:--"
                  min={filters.dateFrom || undefined}
                />
              </div>

              <div title={
                filters.service === 'interactive-configuration'
                  ? 'Status filtering is not applicable for Interactive Discussion service.'
                  : ''
              }>
                <label className="block text-sm font-medium text-semantic-gray-700 mb-2">
                  Session Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  disabled={filters.service === 'interactive-configuration'}
                  className={`w-full border border-semantic-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary ${filters.service === 'interactive-configuration' ? 'bg-semantic-gray-100 cursor-not-allowed text-semantic-gray-500' : ''
                    }`}
                >
                  <option value="all">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-semantic-gray-700 mb-2">
                  Service Type
                </label>
                <select
                  value={filters.service}
                  onChange={(e) => handleFilterChange('service', e.target.value)}
                  className="w-full border border-semantic-gray-300 rounded-md px-3 py-2 text-sm focus:ring-2 focus:ring-primary focus:border-primary"
                >
                  <option value="all">All Service Types</option>
                  <option value="code-generation">Code Generation</option>
                  <option value="code-maintenance">Code Maintenance</option>
                  <option value="deep-query">Deep Query</option>
                  <option value="code-query">Code Query</option>
                  <option value="interactive-configuration">Interactive Discussion</option>
                  <option value="auto-configuration">Auto Configuration</option>
                </select>
              </div>


            </div>
            <div className="w-full flex justify-end space-x-2  mt-4">
              <button
                onClick={applyFilters}
                disabled={filterLoading || chartLoading}
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:bg-primary-400 transition-colors text-sm font-medium flex items-center space-x-2"
              >
                {(filterLoading || chartLoading) && (
                  <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                )}
                <span>Apply Filters</span>
              </button>
              <button
                onClick={clearFilters}
                disabled={filterLoading || chartLoading}
                className="px-4 py-2 bg-semantic-gray-600 text-white rounded-md hover:bg-semantic-gray-700 disabled:bg-semantic-gray-400 transition-colors text-sm font-medium"
              >
                Clear All
              </button>
            </div>


            {/* Filter status indicator */}
            {hasFiltersApplied && (
              <div className="mt-4 p-3 bg-primary-50 border border-primary-200 rounded-md">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-primary text-sm font-medium">🔍 Filters Applied:</span>
                    <div className="flex flex-wrap gap-2 text-xs">
                      {filters.dateFrom && (
                        <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">
                          From: {new Date(filters.dateFrom).toLocaleString()}
                        </span>
                      )}
                      {filters.dateTo && (
                        <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded">
                          To: {new Date(filters.dateTo).toLocaleString()}
                        </span>
                      )}
                      {filters.status !== 'all' && (
                        <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded capitalize">
                          Status: {filters.status}
                        </span>
                      )}

                      {filters.service !== 'all' && (
                        <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded capitalize">
                          Service: {filters.service}
                        </span>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={clearFilters}
                    className="text-primary hover:text-primary-800 text-sm font-medium"
                  >
                    Clear All
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Banner */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error occurred</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error.message}</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={clearError}
                    className="typography-body-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200 transition-colors"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <StatsCards
          stats={currentStats}
          loading={filterLoading && !currentStats}
          error={!currentStats && !filterLoading ? { message: 'No data available' } : null}
        />

        {/* Chart Section - Only show on Overview */}
        {currentView === VIEWS.OVERVIEW && (
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
              <h3 className="text-lg font-semibold text-semantic-gray-900">Activity Chart</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-semantic-gray-700 whitespace-nowrap">Granularity:</label>
                  <select
                    value={granularity}
                    onChange={(e) => setGranularity(e.target.value)}
                    className="border border-semantic-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-primary focus:border-primary bg-white"
                  >
                    <option value="day">Daily</option>
                    <option value="hour">Hourly</option>
                  </select>
                </div>

                {chartData.length > 0 && (
                  <div className="text-sm text-semantic-gray-500">
                    {chartData.length} data point{chartData.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </div>

            <SessionChart
              chartData={chartData}
              loading={chartLoading}
              error={chartError}
              granularity={granularity}
            />
          </div>
        )}

        {/* Breadcrumb Navigation */}
        <Breadcrumb
          currentView={currentView}
          selectedTenant={selectedTenant}
          selectedUser={selectedUser}
          onNavigateToOverview={navigateToOverview}
          onNavigateToTenant={navigateToTenantView}
          onNavigateToUser={navigateToUserView}
        />

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Overview Section */}
            {currentView === VIEWS.OVERVIEW && (
              <div>
                <TenantsList
                  tenants={currentTenants}
                  selectedTenantId={selectedTenant?.id}
                  onTenantSelect={(tenanat) => handleClick(tenanat)}
                  onDropdownChange={handleTenantDropdownChange}
                  loading={filterLoading}
                  error={error}
                />
              </div>
            )}

            {/* Tenant View */}
            {currentView === VIEWS.TENANT && (
              <UsersList
                users={users}
                tenantName={selectedTenant?.name}
                onUserSelect={(user) => handleUsersListClick(user)}
                onBackToOverview={navigateToOverview}
                loading={loading}
                error={error}
              />
            )}

            {/* User Sessions View */}
            {currentView === VIEWS.USER && (
              <SessionsList
                sessions={sessions}
                userId={selectedUser?.name}
                dateFrom={dateFrom}
                dateTo={dateTo}
                onDateFromChange={setDateFrom}
                onDateToChange={setDateTo}
                onFilterSessions={filterSessions}
                onSessionSelect={navigateToSession}
                onBackToUsers={navigateToTenantView}
                loading={loading}
                error={error}
              />
            )}

            {/* Session Details View */}
            {currentView === VIEWS.SESSION && (
              <SessionDetails
                session={sessionDetails}
                onBackToSessions={navigateToUserView}
              />
            )}
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      <LoadingOverlay show={loading && !hasFiltersApplied} />
    </div>
  );
};

export default Page;