"use client";

import {FC, ReactNode, useEffect} from "react";
import Breadcrumb from "@/components/UserOnboarding/Navigation/BreadCrumb";
import GroupHeader from "@/components/UserOnboarding/GroupDetails/GroupHeader";
import GroupsTabNav from "@/components/UserOnboarding/Groups/GroupTabNav";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { fetchGroupDetails } from "@/utils/api";
import { useUser } from "@/components/Context/UserContext";

interface LayoutProps {
    children: ReactNode;
    params: {
        id: string;
    };
}

interface GroupDetails {
    name: string;
    group_type: string;
    description: string;
    status: string;
}

const GroupLayout: FC<LayoutProps> = ({children, params}) => {
    const pathName = usePathname();
    const [groupDetails, setGroupDetails] = useState<GroupDetails | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const { tenant_id } = useUser();

    // Extract group ID using regex or split
    const groupId = params.id;
    // Or alternatively: path.match(/groups\/(grp-[^/]+)/)?.[1];


    useEffect(() => {
        const fetchDetails = async () => {
            try {
                setIsLoading(true);
                const details = await fetchGroupDetails(tenant_id, groupId);
                setGroupDetails(details);
                setIsLoading(false);
            } catch (error) {
                
            }
        };

        fetchDetails();
    }, [groupId]);

    if (!groupId) {
      
      return;
    }
    const getBreadcrumbItems = () => {
        const items = [
            {
                label: "Home",
                href: "/dashboard",
                active: false
            },
            {
                label: "Groups",
                href: "/dashboard/groups",
                active: false
            }
        ];

        if(pathName === `/dashboard/groups/${params.id}`){
            items[1].active = true;
        } else {
            items.push({
                label: groupDetails?.name || "Group",
                href: `dashboard/groups/${params.id}`,
                active: true
            });
        }

        return items;
    }   

    return(
        <div className="flex flex-col h-[88vh]">
            <div className="flex-none w-full max-w-9xl mx-auto px-2 lg:px-2">
                <Breadcrumb items={getBreadcrumbItems()} />

                { isLoading && (
                    <div className="animate-pulse">
                        <div className="flex items-center justify-between mb-1 mt-1">
                            <div className="flex flex-col gap-2">
                                <div className="bg-semantic-gray-200 w-56 p-3 rounded-md"></div>
                                <div className="bg-semantic-gray-200 w-28 p-3 rounded-lg" />
                            </div>
                            <div className="w-24 p-3 rounded-md bg-semantic-gray-200"/>
                        </div>
                    </div>
                )}

                {!isLoading && groupDetails && (
                    <GroupHeader
                        orgId={tenant_id}
                        groupId={groupId}
                        groupDetails={groupDetails}
                        setGroupDetails={setGroupDetails}
                    />
                )}

                <GroupsTabNav groupId={groupId} />
            </div>
            
            <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto custom-scrollbar">
                    <div className="w-full max-w-9xl mx-auto px-2 sm:px-2 lg:px-3 pt-1">
                        {children}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default GroupLayout;