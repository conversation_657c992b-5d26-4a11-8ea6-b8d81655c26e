"use client";

import React, { useState, useEffect, useRef, useCallback, useMemo, useLayoutEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Head from "next/head";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { GitBranch, Sliders } from 'lucide-react';

// Import your contexts
import TabOverflowDropdown from '@/components/BrowsePanel/TabOverflowDropdown';

const TABS = [
  { name: "general", icon: <Sliders className="w-4 h-4" />, label: "General", tooltip: "General settings and preferences" },
  { name: "scm", icon: <GitBranch className="w-4 h-4" />, label: "SCM", tooltip: "Source Control Integration" },];

const TabLayout = ({ children }) => {
  const [transition, setTransition] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const tabContainerRef = useRef(null);
  const tabRefs = useRef([]);

  // State management
  const [visibleTabs, setVisibleTabs] = useState([]);
  const [overflowTabs, setOverflowTabs] = useState([]);

  // Derived state
  const currentTab = pathname.split("/")[3];

  // Regular tab navigation
  const handleTabChange = (value) => {
    let url = `/dashboard/settings/${value}`;
    router.push(url);
  };

  const debounce = (func, delay) => {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), delay);
    };
  };

  // Callback to calculate tab widths
  const calculateTabWidths = useCallback(() => {
    // Ensure tabRefs.current is an array before filtering
    const currentRefs = tabRefs.current || [];

    return currentRefs
      .filter(ref => ref !== null)
      .map(ref => ref.offsetWidth);
  }, []);

  // Responsive tab layout calculation
  const calculateVisibleTabs = useCallback(() => {
    if (!tabContainerRef.current) return;

    const containerWidth = tabContainerRef.current.clientWidth;
    const tabWidths = calculateTabWidths();

    const screenWidth = window.innerWidth;
    // const moreButtonWidth = 150; // Estimated width of the "More" button
    let moreButtonWidth = 150; // Default width for smaller screens

    if (screenWidth > 1200) {
      moreButtonWidth = 210; // Increase width for larger screens (e.g., screens > 1200px wide)
    } else if (screenWidth > 768) {
      moreButtonWidth = 180; // Medium width for tablet-sized screens
    }

    let remainingWidth = containerWidth;
    const fittingTabs = [];
    const extraTabs = [];

    for (let i = 0; i < TABS.length; i++) {
      const tabWidth = tabWidths[i] || 120; // Fallback to estimated width

      if (remainingWidth > tabWidth + moreButtonWidth) {
        fittingTabs.push(TABS[i]);
        remainingWidth -= tabWidth;
      } else {
        extraTabs.push(TABS[i]);
      }
    }

    setVisibleTabs(fittingTabs);
    setOverflowTabs(extraTabs);
  }, [calculateTabWidths]);

  useLayoutEffect(() => {
    calculateVisibleTabs();
  }, [TABS, calculateVisibleTabs]);

  // Use effects
  useEffect(() => {
    const debouncedCalculateTabs = debounce(calculateVisibleTabs, 100);

    const handleResize = () => {
      debouncedCalculateTabs();
    };

    const resizeObserver = new ResizeObserver(() => {
      debouncedCalculateTabs();
    });

    if (tabContainerRef.current) {
      resizeObserver.observe(tabContainerRef.current);
    }

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      resizeObserver.disconnect();
    };
  }, [calculateVisibleTabs]);

  useEffect(() => {
    const timer = setTimeout(() => setTransition(false), 300);
    return () => clearTimeout(timer);
  }, [pathname]);

  useEffect(() => {
    const path = pathname.split("/")[1];
    document.title = `${path.charAt(0).toUpperCase() + path.slice(1)}`;
  }, [pathname]);

  // Memoized tab rendering
  const renderTabs = useMemo(() => {
    return visibleTabs.map((tab, index) => (
      <BootstrapTooltip
        key={tab.name}
        title={tab?.tooltip}
        placement="bottom"
      >
        <button
          ref={(el) => {
            if (!tabRefs.current) {
              tabRefs.current = [];
            }
            tabRefs.current[index] = el;
          }}
          onClick={() => handleTabChange(tab.name)}
          className={`
            flex items-center justify-center gap-2 px-4 py-2.5 h-10
            transform transition-transform duration-500 ease-in-out
            text-semantic-gray-600 hover:bg-semantic-gray-100 ${currentTab == tab.name && "bg-semantic-gray-100 text-semantic-gray-900 font-weight-medium"}
          `}
        >
          {tab.icon}
          <span className="typography-body-sm whitespace-nowrap">{tab.label}</span>
        </button>
      </BootstrapTooltip>
    ));
  }, [visibleTabs, currentTab, handleTabChange]);

  return (
    <div className="flex flex-col">
      <Head>
        <title>
          {TABS.find((tab) => tab.name === currentTab)?.label || "Project"}
        </title>
      </Head>
      {<header className="sticky flex flex-col bg-custom-bg-primary shadow-sm">
        <div
          id="tabContainerLayout"
          ref={tabContainerRef}
          className="relative flex justify-between items-center border-b border-custom-border"
        >
          <div className="flex overflow-x-hidden">
            {renderTabs}
            {overflowTabs.length > 0 && (
              <TabOverflowDropdown
                overflowTabs={overflowTabs}
                handleTabChange={handleTabChange}
              />
            )}
          </div>
        </div>
      </header>}

      <main
        className={`flex-grow transition-opacity justify-center duration-300 ${transition ? "opacity-0" : "opacity-100"}`}
      >
        {children}
      </main>

      <style jsx global>{`
        .TabsList {
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
        .TabsList::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default TabLayout;
